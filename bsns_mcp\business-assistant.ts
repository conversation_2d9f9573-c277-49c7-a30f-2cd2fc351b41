import 'dotenv/config';
import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// Business ROI Calculator Tool
const calculateROI = tool({
  description: 'Calculate Return on Investment (ROI) percentage',
  parameters: z.object({
    initialInvestment: z.number().describe('The initial investment amount'),
    finalValue: z.number().describe('The final value of the investment'),
  }),
  execute: async ({ initialInvestment, finalValue }) => {
    console.log('🔧 Tool executing with:', { initialInvestment, finalValue });

    const roi = ((finalValue - initialInvestment) / initialInvestment) * 100;
    const profit = finalValue - initialInvestment;

    const result = {
      roi: parseFloat(roi.toFixed(2)),
      profit: parseFloat(profit.toFixed(2)),
      message: `ROI: ${roi.toFixed(2)}%, Profit: $${profit.toFixed(2)}`
    };

    console.log('🔧 Tool result:', result);
    return result;
  },
});

// Business Assistant Class
class BusinessAssistant {
  private model = openai('gpt-4o-mini');

  private systemPrompt = `You are a helpful business assistant. You can help with:
  - Business planning and strategy
  - Market analysis
  - Financial calculations
  - Project management advice
  - General business questions

  Always provide practical, actionable advice and be professional in your responses.
  When users mention investments or financial calculations, use the calculateROI tool if appropriate.`;

  async chat(userMessage: string) {
    try {
      const result = await generateText({
        model: this.model,
        system: this.systemPrompt,
        prompt: userMessage,
        tools: {
          calculateROI,
        },
        maxToolRoundtrips: 3,
      });

      console.log('🔍 Full result:', JSON.stringify(result, null, 2));

      return {
        text: result.text,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults,
        usage: result.usage,
      };
    } catch (error) {
      throw new Error(`Business Assistant Error: ${error.message}`);
    }
  }
}

// Example usage
async function main() {
  const assistant = new BusinessAssistant();

  console.log('🚀 Business Assistant Ready!\n');

  // Test 1: General business advice
  console.log('💼 Question: What are the key factors to consider when starting a new business?');
  const response1 = await assistant.chat(
    'What are the key factors to consider when starting a new business? Please provide 3 main points.'
  );
  console.log('📝 Response:');
  console.log(response1.text);
  console.log('\n' + '='.repeat(80) + '\n');

  // Test 2: ROI calculation
  console.log('📊 Question: I invested $10,000 and now my investment is worth $12,500. What is my ROI?');
  const response2 = await assistant.chat(
    'I invested $10,000 and now my investment is worth $12,500. Can you calculate my ROI?'
  );
  console.log('📝 Response:');
  console.log(response2.text);

  if (response2.toolCalls && response2.toolCalls.length > 0) {
    console.log('\n🔧 Tool Calls:');
    response2.toolCalls.forEach((call, index) => {
      console.log(`${index + 1}. Tool: ${call.toolName}`);
      console.log(`   Args:`, call.args);
      console.log(`   Result:`, call.result);
    });
  }

  if (response2.toolResults && response2.toolResults.length > 0) {
    console.log('\n🔧 Tool Results:');
    response2.toolResults.forEach((result, index) => {
      console.log(`${index + 1}. Tool Result:`, result);
    });
  }

  console.log('\n' + '='.repeat(80) + '\n');
  console.log('✅ Business Assistant Demo Complete!');
}

// Run the demo
main().catch(console.error);
