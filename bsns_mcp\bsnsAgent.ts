import { defineAgent } from "@mastra/agent";

export const bsnsAgent = defineAgent({
  id: "bsns-agent",
  name: "İş Dünyası Haber Özeti",
  description: "URL ile iş dünyası haberlerini özetleyen agent.",
  run: async ({ input }) => {
    const url = `https://server.smithery.ai/@KULLANICI_ADIN/bsns-mcp/mcp?api_key=API_KEYINIZ&profile=default`; // kendi smithery url'ini yaz

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ url: input }),
    });

    const result = await response.json();
    return result;
  },
  inputSchema: {
    type: "string",
    description: "Haber linki",
  },
});
