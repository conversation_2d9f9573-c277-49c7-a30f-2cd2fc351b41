import 'dotenv/config';
import { summarizeUrlWithZelihaApi, listAvailableTools, initializeMcpConnection } from './bsnsAgent';

async function testZelihaApi() {
  console.log('🚀 Zeliha\'nın bsns-mcp API Test Başlıyor...\n');
  console.log('🔗 API URL: https://server.smithery.ai/@zeliha1/bsns-mcp/mcp');
  console.log('🔑 API Key: 6134e80e-8e3e-4eb3-8482-ed8542124c32\n');

  try {
    // Test 0: MCP bağlantısını başlat
    console.log('🔗 Test 0: MCP bağlantısını başlat');
    const initResult = await initializeMcpConnection();
    console.log('🔗 Bağlantı Sonucu:');
    console.log(JSON.stringify(initResult, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');

    // Test 1: Mevcut araçları listele
    console.log('🔍 Test 1: Mevcut araçları listele');
    const tools = await listAvailableTools();
    console.log('📋 Mevcut Araçlar:');
    console.log(JSON.stringify(tools, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');

    // Test 2: Basit bir URL ile test
    console.log('📄 Test 2: Example.com URL\'si ile test');
    console.log('URL: https://www.example.com');

    const result1 = await summarizeUrlWithZelihaApi('https://www.example.com');

    console.log('📝 Sonuç:');
    console.log(JSON.stringify(result1, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');

    // Test 3: Haber sitesi ile test
    console.log('📰 Test 3: BBC News URL\'si ile test');
    console.log('URL: https://www.bbc.com/news');

    const result2 = await summarizeUrlWithZelihaApi('https://www.bbc.com/news');

    console.log('📝 Sonuç:');
    console.log(JSON.stringify(result2, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');

    // Test 4: Türkçe haber sitesi ile test
    console.log('🇹🇷 Test 4: Türkçe haber sitesi ile test');
    console.log('URL: https://www.hurriyet.com.tr');

    const result3 = await summarizeUrlWithZelihaApi('https://www.hurriyet.com.tr');

    console.log('📝 Sonuç:');
    console.log(JSON.stringify(result3, null, 2));
    console.log('\n' + '='.repeat(80) + '\n');

    console.log('✅ Tüm testler tamamlandı!');
    console.log('🎉 bsns-mcp API başarıyla bağlandı ve çalışıyor!');

  } catch (error) {
    console.error('❌ Test Hatası:', error);
    console.error('Hata Detayı:', error.message);

    // Hata durumunda troubleshooting önerileri
    console.log('\n🔧 Troubleshooting Önerileri:');
    console.log('1. API key\'in doğru olduğundan emin olun');
    console.log('2. İnternet bağlantınızı kontrol edin');
    console.log('3. bsns-mcp API\'sinin çalıştığından emin olun');
    console.log('4. URL formatının doğru olduğundan emin olun');
  }
}

// Test'i çalıştır
testZelihaApi();
