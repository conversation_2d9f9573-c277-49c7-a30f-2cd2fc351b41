const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/angular-html-LfdN0zeE.js","assets/html-C2L_23MC.js","assets/javascript-ySlJ1b_l.js","assets/css-BPhBrDlE.js","assets/angular-ts-CKsD7JZE.js","assets/scss-C31hgJw-.js","assets/apl-BBq3IX1j.js","assets/xml-e3z08dGr.js","assets/java-xI-RfyKK.js","assets/json-BQoSv7ci.js","assets/astro-CqkE3fuf.js","assets/typescript-Dj6nwHGl.js","assets/postcss-B3ZDOciz.js","assets/blade-a8OxSdnT.js","assets/sql-COK4E0Yg.js","assets/bsl-Dgyn0ogV.js","assets/sdbl-BLhTXw86.js","assets/cairo--RitsXJZ.js","assets/python-DhUJRlN_.js","assets/cobol-PTqiYgYu.js","assets/coffee-dyiR41kL.js","assets/cpp-BksuvNSY.js","assets/regexp-DWJ3fJO_.js","assets/glsl-DBO2IWDn.js","assets/c-C3t2pwGQ.js","assets/crystal-DtDmRg-F.js","assets/shellscript-atvbtKCR.js","assets/edge-D5gP-w-T.js","assets/html-derivative-CSfWNPLT.js","assets/elixir-CLiX3zqd.js","assets/elm-CmHSxxaM.js","assets/erb-BYTLMnw6.js","assets/ruby-DeZ3UC14.js","assets/haml-B2EZWmdv.js","assets/graphql-cDcHW_If.js","assets/jsx-BAng5TT0.js","assets/tsx-B6W0miNI.js","assets/lua-CvWAzNxB.js","assets/yaml-CVw76BM1.js","assets/fortran-fixed-form-TqA4NnZg.js","assets/fortran-free-form-DKXYxT9g.js","assets/fsharp-XplgxFYe.js","assets/markdown-UIAJJxZW.js","assets/gdresource-BHYsBjWJ.js","assets/gdshader-SKMF96pI.js","assets/gdscript-DfxzS6Rs.js","assets/git-commit-i4q6IMui.js","assets/diff-BgYniUM_.js","assets/git-rebase-B-v9cOL2.js","assets/glimmer-js-D-cwc0-E.js","assets/glimmer-ts-pgjy16dm.js","assets/hack-D1yCygmZ.js","assets/handlebars-BQGss363.js","assets/http-FRrOvY1W.js","assets/hxml-TIA70rKU.js","assets/haxe-C5wWYbrZ.js","assets/imba-bv_oIlVt.js","assets/jinja-DGy0s7-h.js","assets/jison-BqZprYcd.js","assets/julia-BBuGR-5E.js","assets/r-CwjWoCRV.js","assets/latex-C-cWTeAZ.js","assets/tex-rYs2v40G.js","assets/liquid-D3W5UaiH.js","assets/marko-z0MBrx5-.js","assets/less-BfCpw3nA.js","assets/mdc-DB_EDNY_.js","assets/nginx-D_VnBJ67.js","assets/nim-ZlGxZxc3.js","assets/perl-CHQXSrWU.js","assets/php-B5ebYQev.js","assets/pug-CM9l7STV.js","assets/qml-D8XfuvdV.js","assets/razor-CNLDkMZG.js","assets/csharp-D9R-vmeu.js","assets/rst-4NLicBqY.js","assets/cmake-DbXoA79R.js","assets/sas-BmTFh92c.js","assets/shaderlab-B7qAK45m.js","assets/hlsl-ifBTmRxC.js","assets/shellsession-C_rIy8kc.js","assets/soy-C-lX7w71.js","assets/sparql-bYkjHRlG.js","assets/turtle-BMR_PYu6.js","assets/stata-DorPZHa4.js","assets/svelte-MSaWC3Je.js","assets/templ-dwX3ZSMB.js","assets/go-B1SYOhNW.js","assets/ts-tags-CipyTH0X.js","assets/twig-NC5TFiHP.js","assets/vue-BuYVFjOK.js","assets/vue-html-xdeiXROB.js","assets/xsl-Dd0NUgwM.js"])))=>i.map(i=>d[i]);
var qt=Object.defineProperty;var zt=(i,e,t)=>e in i?qt(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var _=(i,e,t)=>zt(i,typeof e!="symbol"?e+"":e,t);import{_ as l,w as Me,s as dt,f as Jt,a as Kt,b as Qt,c as Je,h as Xt}from"./index-B7uB4AVM.js";const Ke={}.hasOwnProperty;function Yt(i,e){const t=e||{};function r(n,...s){let o=r.invalid;const c=r.handlers;if(n&&Ke.call(n,i)){const a=String(n[i]);o=Ke.call(c,a)?c[a]:r.unknown}if(o)return o.call(this,n,...s)}return r.handlers=t.handlers||{},r.invalid=t.invalid,r.unknown=t.unknown,r}const pt=[{id:"abap",name:"ABAP",import:()=>l(()=>import("./abap-DsBKuouk.js"),[])},{id:"actionscript-3",name:"ActionScript",import:()=>l(()=>import("./actionscript-3-D_z4Izcz.js"),[])},{id:"ada",name:"Ada",import:()=>l(()=>import("./ada-727ZlQH0.js"),[])},{id:"angular-html",name:"Angular HTML",import:()=>l(()=>import("./angular-html-LfdN0zeE.js").then(i=>i.f),__vite__mapDeps([0,1,2,3]))},{id:"angular-ts",name:"Angular TypeScript",import:()=>l(()=>import("./angular-ts-CKsD7JZE.js"),__vite__mapDeps([4,0,1,2,3,5]))},{id:"apache",name:"Apache Conf",import:()=>l(()=>import("./apache-Dn00JSTd.js"),[])},{id:"apex",name:"Apex",import:()=>l(()=>import("./apex-COJ4H7py.js"),[])},{id:"apl",name:"APL",import:()=>l(()=>import("./apl-BBq3IX1j.js"),__vite__mapDeps([6,1,2,3,7,8,9]))},{id:"applescript",name:"AppleScript",import:()=>l(()=>import("./applescript-Bu5BbsvL.js"),[])},{id:"ara",name:"Ara",import:()=>l(()=>import("./ara-7O62HKoU.js"),[])},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>l(()=>import("./asciidoc-BPT9niGB.js"),[])},{id:"asm",name:"Assembly",import:()=>l(()=>import("./asm-Dhn9LcZ4.js"),[])},{id:"astro",name:"Astro",import:()=>l(()=>import("./astro-CqkE3fuf.js"),__vite__mapDeps([10,9,2,11,3,12]))},{id:"awk",name:"AWK",import:()=>l(()=>import("./awk-eg146-Ew.js"),[])},{id:"ballerina",name:"Ballerina",import:()=>l(()=>import("./ballerina-Du268qiB.js"),[])},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>l(()=>import("./bat-fje9CFhw.js"),[])},{id:"beancount",name:"Beancount",import:()=>l(()=>import("./beancount-BwXTMy5W.js"),[])},{id:"berry",name:"Berry",aliases:["be"],import:()=>l(()=>import("./berry-3xVqZejG.js"),[])},{id:"bibtex",name:"BibTeX",import:()=>l(()=>import("./bibtex-xW4inM5L.js"),[])},{id:"bicep",name:"Bicep",import:()=>l(()=>import("./bicep-DHo0CJ0O.js"),[])},{id:"blade",name:"Blade",import:()=>l(()=>import("./blade-a8OxSdnT.js"),__vite__mapDeps([13,1,2,3,7,8,14,9]))},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>l(()=>import("./bsl-Dgyn0ogV.js"),__vite__mapDeps([15,16]))},{id:"c",name:"C",import:()=>l(()=>import("./c-C3t2pwGQ.js"),[])},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>l(()=>import("./cadence-DNquZEk8.js"),[])},{id:"cairo",name:"Cairo",import:()=>l(()=>import("./cairo--RitsXJZ.js"),__vite__mapDeps([17,18]))},{id:"clarity",name:"Clarity",import:()=>l(()=>import("./clarity-BHOwM8T6.js"),[])},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>l(()=>import("./clojure-DxSadP1t.js"),[])},{id:"cmake",name:"CMake",import:()=>l(()=>import("./cmake-DbXoA79R.js"),[])},{id:"cobol",name:"COBOL",import:()=>l(()=>import("./cobol-PTqiYgYu.js"),__vite__mapDeps([19,1,2,3,8]))},{id:"codeowners",name:"CODEOWNERS",import:()=>l(()=>import("./codeowners-Bp6g37R7.js"),[])},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>l(()=>import("./codeql-sacFqUAJ.js"),[])},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>l(()=>import("./coffee-dyiR41kL.js"),__vite__mapDeps([20,2]))},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>l(()=>import("./common-lisp-C7gG9l05.js"),[])},{id:"coq",name:"Coq",import:()=>l(()=>import("./coq-Dsg_Bt_b.js"),[])},{id:"cpp",name:"C++",aliases:["c++"],import:()=>l(()=>import("./cpp-BksuvNSY.js"),__vite__mapDeps([21,22,23,24,14]))},{id:"crystal",name:"Crystal",import:()=>l(()=>import("./crystal-DtDmRg-F.js"),__vite__mapDeps([25,1,2,3,14,24,26]))},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>l(()=>import("./csharp-D9R-vmeu.js"),[])},{id:"css",name:"CSS",import:()=>l(()=>import("./css-BPhBrDlE.js"),[])},{id:"csv",name:"CSV",import:()=>l(()=>import("./csv-B0qRVHPH.js"),[])},{id:"cue",name:"CUE",import:()=>l(()=>import("./cue-DtFQj3wx.js"),[])},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>l(()=>import("./cypher-m2LEI-9-.js"),[])},{id:"d",name:"D",import:()=>l(()=>import("./d-BoXegm-a.js"),[])},{id:"dart",name:"Dart",import:()=>l(()=>import("./dart-B9wLZaAG.js"),[])},{id:"dax",name:"DAX",import:()=>l(()=>import("./dax-ClGRhx96.js"),[])},{id:"desktop",name:"Desktop",import:()=>l(()=>import("./desktop-DEIpsLCJ.js"),[])},{id:"diff",name:"Diff",import:()=>l(()=>import("./diff-BgYniUM_.js"),[])},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>l(()=>import("./docker-COcR7UxN.js"),[])},{id:"dotenv",name:"dotEnv",import:()=>l(()=>import("./dotenv-BjQB5zDj.js"),[])},{id:"dream-maker",name:"Dream Maker",import:()=>l(()=>import("./dream-maker-C-nORZOA.js"),[])},{id:"edge",name:"Edge",import:()=>l(()=>import("./edge-D5gP-w-T.js"),__vite__mapDeps([27,11,1,2,3,28]))},{id:"elixir",name:"Elixir",import:()=>l(()=>import("./elixir-CLiX3zqd.js"),__vite__mapDeps([29,1,2,3]))},{id:"elm",name:"Elm",import:()=>l(()=>import("./elm-CmHSxxaM.js"),__vite__mapDeps([30,23,24]))},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>l(()=>import("./emacs-lisp-BX77sIaO.js"),[])},{id:"erb",name:"ERB",import:()=>l(()=>import("./erb-BYTLMnw6.js"),__vite__mapDeps([31,1,2,3,32,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]))},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>l(()=>import("./erlang-B-DoSBHF.js"),[])},{id:"fennel",name:"Fennel",import:()=>l(()=>import("./fennel-bCA53EVm.js"),[])},{id:"fish",name:"Fish",import:()=>l(()=>import("./fish-w-ucz2PV.js"),[])},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>l(()=>import("./fluent-Dayu4EKP.js"),[])},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>l(()=>import("./fortran-fixed-form-TqA4NnZg.js"),__vite__mapDeps([39,40]))},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>l(()=>import("./fortran-free-form-DKXYxT9g.js"),[])},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>l(()=>import("./fsharp-XplgxFYe.js"),__vite__mapDeps([41,42]))},{id:"gdresource",name:"GDResource",import:()=>l(()=>import("./gdresource-BHYsBjWJ.js"),__vite__mapDeps([43,44,45]))},{id:"gdscript",name:"GDScript",import:()=>l(()=>import("./gdscript-DfxzS6Rs.js"),[])},{id:"gdshader",name:"GDShader",import:()=>l(()=>import("./gdshader-SKMF96pI.js"),[])},{id:"genie",name:"Genie",import:()=>l(()=>import("./genie-ajMbGru0.js"),[])},{id:"gherkin",name:"Gherkin",import:()=>l(()=>import("./gherkin--30QC5Em.js"),[])},{id:"git-commit",name:"Git Commit Message",import:()=>l(()=>import("./git-commit-i4q6IMui.js"),__vite__mapDeps([46,47]))},{id:"git-rebase",name:"Git Rebase Message",import:()=>l(()=>import("./git-rebase-B-v9cOL2.js"),__vite__mapDeps([48,26]))},{id:"gleam",name:"Gleam",import:()=>l(()=>import("./gleam-B430Bg39.js"),[])},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>l(()=>import("./glimmer-js-D-cwc0-E.js"),__vite__mapDeps([49,2,11,3,1]))},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>l(()=>import("./glimmer-ts-pgjy16dm.js"),__vite__mapDeps([50,11,3,2,1]))},{id:"glsl",name:"GLSL",import:()=>l(()=>import("./glsl-DBO2IWDn.js"),__vite__mapDeps([23,24]))},{id:"gnuplot",name:"Gnuplot",import:()=>l(()=>import("./gnuplot-CM8KxXT1.js"),[])},{id:"go",name:"Go",import:()=>l(()=>import("./go-B1SYOhNW.js"),[])},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>l(()=>import("./graphql-cDcHW_If.js"),__vite__mapDeps([34,2,11,35,36]))},{id:"groovy",name:"Groovy",import:()=>l(()=>import("./groovy-DkBy-JyN.js"),[])},{id:"hack",name:"Hack",import:()=>l(()=>import("./hack-D1yCygmZ.js"),__vite__mapDeps([51,1,2,3,14]))},{id:"haml",name:"Ruby Haml",import:()=>l(()=>import("./haml-B2EZWmdv.js"),__vite__mapDeps([33,2,3]))},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>l(()=>import("./handlebars-BQGss363.js"),__vite__mapDeps([52,1,2,3,38]))},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>l(()=>import("./haskell-BILxekzW.js"),[])},{id:"haxe",name:"Haxe",import:()=>l(()=>import("./haxe-C5wWYbrZ.js"),[])},{id:"hcl",name:"HashiCorp HCL",import:()=>l(()=>import("./hcl-HzYwdGDm.js"),[])},{id:"hjson",name:"Hjson",import:()=>l(()=>import("./hjson-T-Tgc4AT.js"),[])},{id:"hlsl",name:"HLSL",import:()=>l(()=>import("./hlsl-ifBTmRxC.js"),[])},{id:"html",name:"HTML",import:()=>l(()=>import("./html-C2L_23MC.js"),__vite__mapDeps([1,2,3]))},{id:"html-derivative",name:"HTML (Derivative)",import:()=>l(()=>import("./html-derivative-CSfWNPLT.js"),__vite__mapDeps([28,1,2,3]))},{id:"http",name:"HTTP",import:()=>l(()=>import("./http-FRrOvY1W.js"),__vite__mapDeps([53,26,9,7,8,34,2,11,35,36]))},{id:"hxml",name:"HXML",import:()=>l(()=>import("./hxml-TIA70rKU.js"),__vite__mapDeps([54,55]))},{id:"hy",name:"Hy",import:()=>l(()=>import("./hy-BMj5Y0dO.js"),[])},{id:"imba",name:"Imba",import:()=>l(()=>import("./imba-bv_oIlVt.js"),__vite__mapDeps([56,11]))},{id:"ini",name:"INI",aliases:["properties"],import:()=>l(()=>import("./ini-BjABl1g7.js"),[])},{id:"java",name:"Java",import:()=>l(()=>import("./java-xI-RfyKK.js"),[])},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>l(()=>import("./javascript-ySlJ1b_l.js"),[])},{id:"jinja",name:"Jinja",import:()=>l(()=>import("./jinja-DGy0s7-h.js"),__vite__mapDeps([57,1,2,3]))},{id:"jison",name:"Jison",import:()=>l(()=>import("./jison-BqZprYcd.js"),__vite__mapDeps([58,2]))},{id:"json",name:"JSON",import:()=>l(()=>import("./json-BQoSv7ci.js"),[])},{id:"json5",name:"JSON5",import:()=>l(()=>import("./json5-w8dY5SsB.js"),[])},{id:"jsonc",name:"JSON with Comments",import:()=>l(()=>import("./jsonc-TU54ms6u.js"),[])},{id:"jsonl",name:"JSON Lines",import:()=>l(()=>import("./jsonl-DREVFZK8.js"),[])},{id:"jsonnet",name:"Jsonnet",import:()=>l(()=>import("./jsonnet-BfivnA6A.js"),[])},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>l(()=>import("./jssm-P4WzXJd0.js"),[])},{id:"jsx",name:"JSX",import:()=>l(()=>import("./jsx-BAng5TT0.js"),[])},{id:"julia",name:"Julia",aliases:["jl"],import:()=>l(()=>import("./julia-BBuGR-5E.js"),__vite__mapDeps([59,21,22,23,24,14,18,2,60]))},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>l(()=>import("./kotlin-B5lbUyaz.js"),[])},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>l(()=>import("./kusto-mebxcVVE.js"),[])},{id:"latex",name:"LaTeX",import:()=>l(()=>import("./latex-C-cWTeAZ.js"),__vite__mapDeps([61,62,60]))},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>l(()=>import("./lean-XBlWyCtg.js"),[])},{id:"less",name:"Less",import:()=>l(()=>import("./less-BfCpw3nA.js"),[])},{id:"liquid",name:"Liquid",import:()=>l(()=>import("./liquid-D3W5UaiH.js"),__vite__mapDeps([63,1,2,3,9]))},{id:"log",name:"Log file",import:()=>l(()=>import("./log-Cc5clBb7.js"),[])},{id:"logo",name:"Logo",import:()=>l(()=>import("./logo-IuBKFhSY.js"),[])},{id:"lua",name:"Lua",import:()=>l(()=>import("./lua-CvWAzNxB.js"),__vite__mapDeps([37,24]))},{id:"luau",name:"Luau",import:()=>l(()=>import("./luau-Du5NY7AG.js"),[])},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>l(()=>import("./make-Bvotw-X0.js"),[])},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>l(()=>import("./markdown-UIAJJxZW.js"),[])},{id:"marko",name:"Marko",import:()=>l(()=>import("./marko-z0MBrx5-.js"),__vite__mapDeps([64,3,65,5,2]))},{id:"matlab",name:"MATLAB",import:()=>l(()=>import("./matlab-D9-PGadD.js"),[])},{id:"mdc",name:"MDC",import:()=>l(()=>import("./mdc-DB_EDNY_.js"),__vite__mapDeps([66,42,38,28,1,2,3]))},{id:"mdx",name:"MDX",import:()=>l(()=>import("./mdx-sdHcTMYB.js"),[])},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>l(()=>import("./mermaid-Ci6OQyBP.js"),[])},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>l(()=>import("./mipsasm-BC5c_5Pe.js"),[])},{id:"mojo",name:"Mojo",import:()=>l(()=>import("./mojo-Tz6hzZYG.js"),[])},{id:"move",name:"Move",import:()=>l(()=>import("./move-DB_GagMm.js"),[])},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>l(()=>import("./narrat-DLbgOhZU.js"),[])},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>l(()=>import("./nextflow-B0XVJmRM.js"),[])},{id:"nginx",name:"Nginx",import:()=>l(()=>import("./nginx-D_VnBJ67.js"),__vite__mapDeps([67,37,24]))},{id:"nim",name:"Nim",import:()=>l(()=>import("./nim-ZlGxZxc3.js"),__vite__mapDeps([68,24,1,2,3,7,8,23,42]))},{id:"nix",name:"Nix",import:()=>l(()=>import("./nix-shcSOmrb.js"),[])},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>l(()=>import("./nushell-D4Tzg5kh.js"),[])},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>l(()=>import("./objective-c-Deuh7S70.js"),[])},{id:"objective-cpp",name:"Objective-C++",import:()=>l(()=>import("./objective-cpp-BUEGK8hf.js"),[])},{id:"ocaml",name:"OCaml",import:()=>l(()=>import("./ocaml-BNioltXt.js"),[])},{id:"pascal",name:"Pascal",import:()=>l(()=>import("./pascal-JqZropPD.js"),[])},{id:"perl",name:"Perl",import:()=>l(()=>import("./perl-CHQXSrWU.js"),__vite__mapDeps([69,1,2,3,7,8,14]))},{id:"php",name:"PHP",import:()=>l(()=>import("./php-B5ebYQev.js"),__vite__mapDeps([70,1,2,3,7,8,14,9]))},{id:"plsql",name:"PL/SQL",import:()=>l(()=>import("./plsql-LKU2TuZ1.js"),[])},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>l(()=>import("./po-BFLt1xDp.js"),[])},{id:"polar",name:"Polar",import:()=>l(()=>import("./polar-DKykz6zU.js"),[])},{id:"postcss",name:"PostCSS",import:()=>l(()=>import("./postcss-B3ZDOciz.js"),[])},{id:"powerquery",name:"PowerQuery",import:()=>l(()=>import("./powerquery-CSHBycmS.js"),[])},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>l(()=>import("./powershell-BIEUsx6d.js"),[])},{id:"prisma",name:"Prisma",import:()=>l(()=>import("./prisma-B48N-Iqd.js"),[])},{id:"prolog",name:"Prolog",import:()=>l(()=>import("./prolog-BY-TUvya.js"),[])},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>l(()=>import("./proto-zocC4JxJ.js"),[])},{id:"pug",name:"Pug",aliases:["jade"],import:()=>l(()=>import("./pug-CM9l7STV.js"),__vite__mapDeps([71,2,3,1]))},{id:"puppet",name:"Puppet",import:()=>l(()=>import("./puppet-Cza_XSSt.js"),[])},{id:"purescript",name:"PureScript",import:()=>l(()=>import("./purescript-Bg-kzb6g.js"),[])},{id:"python",name:"Python",aliases:["py"],import:()=>l(()=>import("./python-DhUJRlN_.js"),[])},{id:"qml",name:"QML",import:()=>l(()=>import("./qml-D8XfuvdV.js"),__vite__mapDeps([72,2]))},{id:"qmldir",name:"QML Directory",import:()=>l(()=>import("./qmldir-C8lEn-DE.js"),[])},{id:"qss",name:"Qt Style Sheets",import:()=>l(()=>import("./qss-DhMKtDLN.js"),[])},{id:"r",name:"R",import:()=>l(()=>import("./r-CwjWoCRV.js"),[])},{id:"racket",name:"Racket",import:()=>l(()=>import("./racket-CzouJOBO.js"),[])},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>l(()=>import("./raku-B1bQXN8T.js"),[])},{id:"razor",name:"ASP.NET Razor",import:()=>l(()=>import("./razor-CNLDkMZG.js"),__vite__mapDeps([73,1,2,3,74]))},{id:"reg",name:"Windows Registry Script",import:()=>l(()=>import("./reg-5LuOXUq_.js"),[])},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>l(()=>import("./regexp-DWJ3fJO_.js"),[])},{id:"rel",name:"Rel",import:()=>l(()=>import("./rel-DJlmqQ1C.js"),[])},{id:"riscv",name:"RISC-V",import:()=>l(()=>import("./riscv-QhoSD0DR.js"),[])},{id:"rst",name:"reStructuredText",import:()=>l(()=>import("./rst-4NLicBqY.js"),__vite__mapDeps([75,28,1,2,3,21,22,23,24,14,18,26,38,76,32,33,7,8,34,11,35,36,37]))},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>l(()=>import("./ruby-DeZ3UC14.js"),__vite__mapDeps([32,1,2,3,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]))},{id:"rust",name:"Rust",aliases:["rs"],import:()=>l(()=>import("./rust-Be6lgOlo.js"),[])},{id:"sas",name:"SAS",import:()=>l(()=>import("./sas-BmTFh92c.js"),__vite__mapDeps([77,14]))},{id:"sass",name:"Sass",import:()=>l(()=>import("./sass-BJ4Li9vH.js"),[])},{id:"scala",name:"Scala",import:()=>l(()=>import("./scala-DQVVAn-B.js"),[])},{id:"scheme",name:"Scheme",import:()=>l(()=>import("./scheme-BJGe-b2p.js"),[])},{id:"scss",name:"SCSS",import:()=>l(()=>import("./scss-C31hgJw-.js"),__vite__mapDeps([5,3]))},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>l(()=>import("./sdbl-BLhTXw86.js"),[])},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>l(()=>import("./shaderlab-B7qAK45m.js"),__vite__mapDeps([78,79]))},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>l(()=>import("./shellscript-atvbtKCR.js"),[])},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>l(()=>import("./shellsession-C_rIy8kc.js"),__vite__mapDeps([80,26]))},{id:"smalltalk",name:"Smalltalk",import:()=>l(()=>import("./smalltalk-DkLiglaE.js"),[])},{id:"solidity",name:"Solidity",import:()=>l(()=>import("./solidity-C1w2a3ep.js"),[])},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>l(()=>import("./soy-C-lX7w71.js"),__vite__mapDeps([81,1,2,3]))},{id:"sparql",name:"SPARQL",import:()=>l(()=>import("./sparql-bYkjHRlG.js"),__vite__mapDeps([82,83]))},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>l(()=>import("./splunk-Cf8iN4DR.js"),[])},{id:"sql",name:"SQL",import:()=>l(()=>import("./sql-COK4E0Yg.js"),[])},{id:"ssh-config",name:"SSH Config",import:()=>l(()=>import("./ssh-config-BknIz3MU.js"),[])},{id:"stata",name:"Stata",import:()=>l(()=>import("./stata-DorPZHa4.js"),__vite__mapDeps([84,14]))},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>l(()=>import("./stylus-BeQkCIfX.js"),[])},{id:"svelte",name:"Svelte",import:()=>l(()=>import("./svelte-MSaWC3Je.js"),__vite__mapDeps([85,2,11,3,12]))},{id:"swift",name:"Swift",import:()=>l(()=>import("./swift-BSxZ-RaX.js"),[])},{id:"system-verilog",name:"SystemVerilog",import:()=>l(()=>import("./system-verilog-C7L56vO4.js"),[])},{id:"systemd",name:"Systemd Units",import:()=>l(()=>import("./systemd-CUnW07Te.js"),[])},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>l(()=>import("./talonscript-C1XDQQGZ.js"),[])},{id:"tasl",name:"Tasl",import:()=>l(()=>import("./tasl-CQjiPCtT.js"),[])},{id:"tcl",name:"Tcl",import:()=>l(()=>import("./tcl-DQ1-QYvQ.js"),[])},{id:"templ",name:"Templ",import:()=>l(()=>import("./templ-dwX3ZSMB.js"),__vite__mapDeps([86,87,2,3]))},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>l(()=>import("./terraform-BbSNqyBO.js"),[])},{id:"tex",name:"TeX",import:()=>l(()=>import("./tex-rYs2v40G.js"),__vite__mapDeps([62,60]))},{id:"toml",name:"TOML",import:()=>l(()=>import("./toml-CB2ApiWb.js"),[])},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>l(()=>import("./ts-tags-CipyTH0X.js"),__vite__mapDeps([88,11,3,2,23,24,1,14,7,8]))},{id:"tsv",name:"TSV",import:()=>l(()=>import("./tsv-B_m7g4N7.js"),[])},{id:"tsx",name:"TSX",import:()=>l(()=>import("./tsx-B6W0miNI.js"),[])},{id:"turtle",name:"Turtle",import:()=>l(()=>import("./turtle-BMR_PYu6.js"),[])},{id:"twig",name:"Twig",import:()=>l(()=>import("./twig-NC5TFiHP.js"),__vite__mapDeps([89,3,2,5,70,1,7,8,14,9,18,32,33,34,11,35,36,21,22,23,24,26,37,38]))},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>l(()=>import("./typescript-Dj6nwHGl.js"),[])},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>l(()=>import("./typespec-BpWG_bgh.js"),[])},{id:"typst",name:"Typst",aliases:["typ"],import:()=>l(()=>import("./typst-BVUVsWT6.js"),[])},{id:"v",name:"V",import:()=>l(()=>import("./v-CAQ2eGtk.js"),[])},{id:"vala",name:"Vala",import:()=>l(()=>import("./vala-BFOHcciG.js"),[])},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>l(()=>import("./vb-CdO5JTpU.js"),[])},{id:"verilog",name:"Verilog",import:()=>l(()=>import("./verilog-CJaU5se_.js"),[])},{id:"vhdl",name:"VHDL",import:()=>l(()=>import("./vhdl-DYoNaHQp.js"),[])},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>l(()=>import("./viml-m4uW47V2.js"),[])},{id:"vue",name:"Vue",import:()=>l(()=>import("./vue-BuYVFjOK.js"),__vite__mapDeps([90,1,2,3,11,9,28]))},{id:"vue-html",name:"Vue HTML",import:()=>l(()=>import("./vue-html-xdeiXROB.js"),__vite__mapDeps([91,90,1,2,3,11,9,28]))},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>l(()=>import("./vyper-nyqBNV6O.js"),[])},{id:"wasm",name:"WebAssembly",import:()=>l(()=>import("./wasm-C6j12Q_x.js"),[])},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>l(()=>import("./wenyan-7A4Fjokl.js"),[])},{id:"wgsl",name:"WGSL",import:()=>l(()=>import("./wgsl-CB0Krxn9.js"),[])},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>l(()=>import("./wikitext-DCE3LsBG.js"),[])},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>l(()=>import("./wolfram-C3FkfJm5.js"),[])},{id:"xml",name:"XML",import:()=>l(()=>import("./xml-e3z08dGr.js"),__vite__mapDeps([7,8]))},{id:"xsl",name:"XSL",import:()=>l(()=>import("./xsl-Dd0NUgwM.js"),__vite__mapDeps([92,7,8]))},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>l(()=>import("./yaml-CVw76BM1.js"),[])},{id:"zenscript",name:"ZenScript",import:()=>l(()=>import("./zenscript-HnGAYVZD.js"),[])},{id:"zig",name:"Zig",import:()=>l(()=>import("./zig-BVz_zdnA.js"),[])}],Zt=Object.fromEntries(pt.map(i=>[i.id,i.import])),er=Object.fromEntries(pt.flatMap(i=>{var e;return((e=i.aliases)==null?void 0:e.map(t=>[t,i.import]))||[]})),tr={...Zt,...er},rr=[{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>l(()=>import("./andromeeda-C3khCPGq.js"),[])},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>l(()=>import("./aurora-x-D-2ljcwZ.js"),[])},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>l(()=>import("./ayu-dark-Cv9koXgw.js"),[])},{id:"catppuccin-frappe",displayName:"Catppuccin Frappé",type:"dark",import:()=>l(()=>import("./catppuccin-frappe-CD_QflpE.js"),[])},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>l(()=>import("./catppuccin-latte-DRW-0cLl.js"),[])},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>l(()=>import("./catppuccin-macchiato-C-_shW-Y.js"),[])},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>l(()=>import("./catppuccin-mocha-LGGdnPYs.js"),[])},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>l(()=>import("./dark-plus-C3mMm8J8.js"),[])},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>l(()=>import("./dracula-BzJJZx-M.js"),[])},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>l(()=>import("./dracula-soft-BXkSAIEj.js"),[])},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>l(()=>import("./everforest-dark-BgDCqdQA.js"),[])},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>l(()=>import("./everforest-light-C8M2exoo.js"),[])},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>l(()=>import("./github-dark-DHJKELXO.js"),[])},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>l(()=>import("./github-dark-default-Cuk6v7N8.js"),[])},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>l(()=>import("./github-dark-dimmed-DH5Ifo-i.js"),[])},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>l(()=>import("./github-dark-high-contrast-E3gJ1_iC.js"),[])},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>l(()=>import("./github-light-DAi9KRSo.js"),[])},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>l(()=>import("./github-light-default-D7oLnXFd.js"),[])},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>l(()=>import("./github-light-high-contrast-BfjtVDDH.js"),[])},{id:"houston",displayName:"Houston",type:"dark",import:()=>l(()=>import("./houston-DnULxvSX.js"),[])},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>l(()=>import("./kanagawa-dragon-CkXjmgJE.js"),[])},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>l(()=>import("./kanagawa-lotus-CfQXZHmo.js"),[])},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>l(()=>import("./kanagawa-wave-DWedfzmr.js"),[])},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>l(()=>import("./laserwave-DUszq2jm.js"),[])},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>l(()=>import("./light-plus-B7mTdjB0.js"),[])},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>l(()=>import("./material-theme-D5KoaKCx.js"),[])},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>l(()=>import("./material-theme-darker-BfHTSMKl.js"),[])},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>l(()=>import("./material-theme-lighter-B0m2ddpp.js"),[])},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>l(()=>import("./material-theme-ocean-CyktbL80.js"),[])},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>l(()=>import("./material-theme-palenight-Csfq5Kiy.js"),[])},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>l(()=>import("./min-dark-CafNBF8u.js"),[])},{id:"min-light",displayName:"Min Light",type:"light",import:()=>l(()=>import("./min-light-CTRr51gU.js"),[])},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>l(()=>import("./monokai-D4h5O-jR.js"),[])},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>l(()=>import("./night-owl-C39BiMTA.js"),[])},{id:"nord",displayName:"Nord",type:"dark",import:()=>l(()=>import("./nord-Ddv68eIx.js"),[])},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>l(()=>import("./one-dark-pro-GBQ2dnAY.js"),[])},{id:"one-light",displayName:"One Light",type:"light",import:()=>l(()=>import("./one-light-PoHY5YXO.js"),[])},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>l(()=>import("./plastic-3e1v2bzS.js"),[])},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>l(()=>import("./poimandres-CS3Unz2-.js"),[])},{id:"red",displayName:"Red",type:"dark",import:()=>l(()=>import("./red-bN70gL4F.js"),[])},{id:"rose-pine",displayName:"Rosé Pine",type:"dark",import:()=>l(()=>import("./rose-pine-CmCqftbK.js"),[])},{id:"rose-pine-dawn",displayName:"Rosé Pine Dawn",type:"light",import:()=>l(()=>import("./rose-pine-dawn-Ds-gbosJ.js"),[])},{id:"rose-pine-moon",displayName:"Rosé Pine Moon",type:"dark",import:()=>l(()=>import("./rose-pine-moon-CjDtw9vr.js"),[])},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>l(()=>import("./slack-dark-BthQWCQV.js"),[])},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>l(()=>import("./slack-ochin-DqwNpetd.js"),[])},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>l(()=>import("./snazzy-light-Bw305WKR.js"),[])},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>l(()=>import("./solarized-dark-DXbdFlpD.js"),[])},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>l(()=>import("./solarized-light-L9t79GZl.js"),[])},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>l(()=>import("./synthwave-84-CbfX1IO0.js"),[])},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>l(()=>import("./tokyo-night-DBQeEorK.js"),[])},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>l(()=>import("./vesper-BEBZ7ncR.js"),[])},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>l(()=>import("./vitesse-black-Bkuqu6BP.js"),[])},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>l(()=>import("./vitesse-dark-D0r3Knsf.js"),[])},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>l(()=>import("./vitesse-light-CVO1_9PV.js"),[])}],ir=Object.fromEntries(rr.map(i=>[i.id,i.import]));let B=class extends Error{constructor(e){super(e),this.name="ShikiError"}},Be=class extends Error{constructor(e){super(e),this.name="ShikiError"}};function nr(){return 2147483648}function sr(){return typeof performance<"u"?performance.now():Date.now()}const or=(i,e)=>i+(e-i%e)%e;async function ar(i){let e,t;const r={};function n(d){t=d,r.HEAPU8=new Uint8Array(d),r.HEAPU32=new Uint32Array(d)}function s(d,f,T){r.HEAPU8.copyWithin(d,f,f+T)}function o(d){try{return e.grow(d-t.byteLength+65535>>>16),n(e.buffer),1}catch{}}function c(d){const f=r.HEAPU8.length;d=d>>>0;const T=nr();if(d>T)return!1;for(let E=1;E<=4;E*=2){let g=f*(1+.2/E);g=Math.min(g,d+100663296);const y=Math.min(T,or(Math.max(d,g),65536));if(o(y))return!0}return!1}const a=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function u(d,f,T=1024){const E=f+T;let g=f;for(;d[g]&&!(g>=E);)++g;if(g-f>16&&d.buffer&&a)return a.decode(d.subarray(f,g));let y="";for(;f<g;){let R=d[f++];if(!(R&128)){y+=String.fromCharCode(R);continue}const v=d[f++]&63;if((R&224)===192){y+=String.fromCharCode((R&31)<<6|v);continue}const A=d[f++]&63;if((R&240)===224?R=(R&15)<<12|v<<6|A:R=(R&7)<<18|v<<12|A<<6|d[f++]&63,R<65536)y+=String.fromCharCode(R);else{const w=R-65536;y+=String.fromCharCode(55296|w>>10,56320|w&1023)}}return y}function m(d,f){return d?u(r.HEAPU8,d,f):""}const h={emscripten_get_now:sr,emscripten_memcpy_big:s,emscripten_resize_heap:c,fd_write:()=>0};async function p(){const f=await i({env:h,wasi_snapshot_preview1:h});e=f.memory,n(e.buffer),Object.assign(r,f),r.UTF8ToString=m}return await p(),r}var cr=Object.defineProperty,lr=(i,e,t)=>e in i?cr(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,P=(i,e,t)=>(lr(i,typeof e!="symbol"?e+"":e,t),t);let I=null;function ur(i){throw new Be(i.UTF8ToString(i.getLastOnigError()))}class ye{constructor(e){P(this,"utf16Length"),P(this,"utf8Length"),P(this,"utf16Value"),P(this,"utf8Value"),P(this,"utf16OffsetToUtf8"),P(this,"utf8OffsetToUtf16");const t=e.length,r=ye._utf8ByteLength(e),n=r!==t,s=n?new Uint32Array(t+1):null;n&&(s[t]=r);const o=n?new Uint32Array(r+1):null;n&&(o[r]=t);const c=new Uint8Array(r);let a=0;for(let u=0;u<t;u++){const m=e.charCodeAt(u);let h=m,p=!1;if(m>=55296&&m<=56319&&u+1<t){const d=e.charCodeAt(u+1);d>=56320&&d<=57343&&(h=(m-55296<<10)+65536|d-56320,p=!0)}n&&(s[u]=a,p&&(s[u+1]=a),h<=127?o[a+0]=u:h<=2047?(o[a+0]=u,o[a+1]=u):h<=65535?(o[a+0]=u,o[a+1]=u,o[a+2]=u):(o[a+0]=u,o[a+1]=u,o[a+2]=u,o[a+3]=u)),h<=127?c[a++]=h:h<=2047?(c[a++]=192|(h&1984)>>>6,c[a++]=128|(h&63)>>>0):h<=65535?(c[a++]=224|(h&61440)>>>12,c[a++]=128|(h&4032)>>>6,c[a++]=128|(h&63)>>>0):(c[a++]=240|(h&1835008)>>>18,c[a++]=128|(h&258048)>>>12,c[a++]=128|(h&4032)>>>6,c[a++]=128|(h&63)>>>0),p&&u++}this.utf16Length=t,this.utf8Length=r,this.utf16Value=e,this.utf8Value=c,this.utf16OffsetToUtf8=s,this.utf8OffsetToUtf16=o}static _utf8ByteLength(e){let t=0;for(let r=0,n=e.length;r<n;r++){const s=e.charCodeAt(r);let o=s,c=!1;if(s>=55296&&s<=56319&&r+1<n){const a=e.charCodeAt(r+1);a>=56320&&a<=57343&&(o=(s-55296<<10)+65536|a-56320,c=!0)}o<=127?t+=1:o<=2047?t+=2:o<=65535?t+=3:t+=4,c&&r++}return t}createString(e){const t=e.omalloc(this.utf8Length);return e.HEAPU8.set(this.utf8Value,t),t}}const D=class{constructor(i){if(P(this,"id",++D.LAST_ID),P(this,"_onigBinding"),P(this,"content"),P(this,"utf16Length"),P(this,"utf8Length"),P(this,"utf16OffsetToUtf8"),P(this,"utf8OffsetToUtf16"),P(this,"ptr"),!I)throw new Be("Must invoke loadWasm first.");this._onigBinding=I,this.content=i;const e=new ye(i);this.utf16Length=e.utf16Length,this.utf8Length=e.utf8Length,this.utf16OffsetToUtf8=e.utf16OffsetToUtf8,this.utf8OffsetToUtf16=e.utf8OffsetToUtf16,this.utf8Length<1e4&&!D._sharedPtrInUse?(D._sharedPtr||(D._sharedPtr=I.omalloc(1e4)),D._sharedPtrInUse=!0,I.HEAPU8.set(e.utf8Value,D._sharedPtr),this.ptr=D._sharedPtr):this.ptr=e.createString(I)}convertUtf8OffsetToUtf16(i){return this.utf8OffsetToUtf16?i<0?0:i>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[i]:i}convertUtf16OffsetToUtf8(i){return this.utf16OffsetToUtf8?i<0?0:i>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[i]:i}dispose(){this.ptr===D._sharedPtr?D._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}};let ee=D;P(ee,"LAST_ID",0);P(ee,"_sharedPtr",0);P(ee,"_sharedPtrInUse",!1);class mr{constructor(e){if(P(this,"_onigBinding"),P(this,"_ptr"),!I)throw new Be("Must invoke loadWasm first.");const t=[],r=[];for(let c=0,a=e.length;c<a;c++){const u=new ye(e[c]);t[c]=u.createString(I),r[c]=u.utf8Length}const n=I.omalloc(4*e.length);I.HEAPU32.set(t,n/4);const s=I.omalloc(4*e.length);I.HEAPU32.set(r,s/4);const o=I.createOnigScanner(n,s,e.length);for(let c=0,a=e.length;c<a;c++)I.ofree(t[c]);I.ofree(s),I.ofree(n),o===0&&ur(I),this._onigBinding=I,this._ptr=o}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(e,t,r){let n=0;if(typeof r=="number"&&(n=r),typeof e=="string"){e=new ee(e);const s=this._findNextMatchSync(e,t,!1,n);return e.dispose(),s}return this._findNextMatchSync(e,t,!1,n)}_findNextMatchSync(e,t,r,n){const s=this._onigBinding,o=s.findNextOnigScannerMatch(this._ptr,e.id,e.ptr,e.utf8Length,e.convertUtf16OffsetToUtf8(t),n);if(o===0)return null;const c=s.HEAPU32;let a=o/4;const u=c[a++],m=c[a++],h=[];for(let p=0;p<m;p++){const d=e.convertUtf8OffsetToUtf16(c[a++]),f=e.convertUtf8OffsetToUtf16(c[a++]);h[p]={start:d,end:f,length:f-d}}return{index:u,captureIndices:h}}}function hr(i){return typeof i.instantiator=="function"}function dr(i){return typeof i.default=="function"}function pr(i){return typeof i.data<"u"}function _r(i){return typeof Response<"u"&&i instanceof Response}function fr(i){var e;return typeof ArrayBuffer<"u"&&(i instanceof ArrayBuffer||ArrayBuffer.isView(i))||typeof Buffer<"u"&&((e=Buffer.isBuffer)==null?void 0:e.call(Buffer,i))||typeof SharedArrayBuffer<"u"&&i instanceof SharedArrayBuffer||typeof Uint32Array<"u"&&i instanceof Uint32Array}let ie;function gr(i){if(ie)return ie;async function e(){I=await ar(async t=>{let r=i;return r=await r,typeof r=="function"&&(r=await r(t)),typeof r=="function"&&(r=await r(t)),hr(r)?r=await r.instantiator(t):dr(r)?r=await r.default(t):(pr(r)&&(r=r.data),_r(r)?typeof WebAssembly.instantiateStreaming=="function"?r=await Er(r)(t):r=await yr(r)(t):fr(r)?r=await Le(r)(t):r instanceof WebAssembly.Module?r=await Le(r)(t):"default"in r&&r.default instanceof WebAssembly.Module&&(r=await Le(r.default)(t))),"instance"in r&&(r=r.instance),"exports"in r&&(r=r.exports),r})}return ie=e(),ie}function Le(i){return e=>WebAssembly.instantiate(i,e)}function Er(i){return e=>WebAssembly.instantiateStreaming(i,e)}function yr(i){return async e=>{const t=await i.arrayBuffer();return WebAssembly.instantiate(t,e)}}let Rr;function Tr(){return Rr}async function _t(i){return i&&await gr(i),{createScanner(e){return new mr(e.map(t=>typeof t=="string"?t:t.source))},createString(e){return new ee(e)}}}function vr(i){return je(i)}function je(i){return Array.isArray(i)?Ar(i):i instanceof RegExp?i:typeof i=="object"?Lr(i):i}function Ar(i){let e=[];for(let t=0,r=i.length;t<r;t++)e[t]=je(i[t]);return e}function Lr(i){let e={};for(let t in i)e[t]=je(i[t]);return e}function ft(i,...e){return e.forEach(t=>{for(let r in t)i[r]=t[r]}),i}function gt(i){const e=~i.lastIndexOf("/")||~i.lastIndexOf("\\");return e===0?i:~e===i.length-1?gt(i.substring(0,i.length-1)):i.substr(~e+1)}var Pe=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,ne=class{static hasCaptures(i){return i===null?!1:(Pe.lastIndex=0,Pe.test(i))}static replaceCaptures(i,e,t){return i.replace(Pe,(r,n,s,o)=>{let c=t[parseInt(n||s,10)];if(c){let a=e.substring(c.start,c.end);for(;a[0]===".";)a=a.substring(1);switch(o){case"downcase":return a.toLowerCase();case"upcase":return a.toUpperCase();default:return a}}else return r})}};function Et(i,e){return i<e?-1:i>e?1:0}function yt(i,e){if(i===null&&e===null)return 0;if(!i)return-1;if(!e)return 1;let t=i.length,r=e.length;if(t===r){for(let n=0;n<t;n++){let s=Et(i[n],e[n]);if(s!==0)return s}return 0}return t-r}function Qe(i){return!!(/^#[0-9a-f]{6}$/i.test(i)||/^#[0-9a-f]{8}$/i.test(i)||/^#[0-9a-f]{3}$/i.test(i)||/^#[0-9a-f]{4}$/i.test(i))}function Rt(i){return i.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var Tt=class{constructor(i){_(this,"cache",new Map);this.fn=i}get(i){if(this.cache.has(i))return this.cache.get(i);const e=this.fn(i);return this.cache.set(i,e),e}},le=class{constructor(i,e,t){_(this,"_cachedMatchRoot",new Tt(i=>this._root.match(i)));this._colorMap=i,this._defaults=e,this._root=t}static createFromRawTheme(i,e){return this.createFromParsedTheme(Ir(i),e)}static createFromParsedTheme(i,e){return Or(i,e)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(i){if(i===null)return this._defaults;const e=i.scopeName,r=this._cachedMatchRoot.get(e).find(n=>Pr(i.parent,n.parentScopes));return r?new vt(r.fontStyle,r.foreground,r.background):null}},be=class ae{constructor(e,t){this.parent=e,this.scopeName=t}static push(e,t){for(const r of t)e=new ae(e,r);return e}static from(...e){let t=null;for(let r=0;r<e.length;r++)t=new ae(t,e[r]);return t}push(e){return new ae(this,e)}getSegments(){let e=this;const t=[];for(;e;)t.push(e.scopeName),e=e.parent;return t.reverse(),t}toString(){return this.getSegments().join(" ")}extends(e){return this===e?!0:this.parent===null?!1:this.parent.extends(e)}getExtensionIfDefined(e){const t=[];let r=this;for(;r&&r!==e;)t.push(r.scopeName),r=r.parent;return r===e?t.reverse():void 0}};function Pr(i,e){if(e.length===0)return!0;for(let t=0;t<e.length;t++){let r=e[t],n=!1;if(r===">"){if(t===e.length-1)return!1;r=e[++t],n=!0}for(;i&&!br(i.scopeName,r);){if(n)return!1;i=i.parent}if(!i)return!1;i=i.parent}return!0}function br(i,e){return e===i||i.startsWith(e)&&i[e.length]==="."}var vt=class{constructor(i,e,t){this.fontStyle=i,this.foregroundId=e,this.backgroundId=t}};function Ir(i){if(!i)return[];if(!i.settings||!Array.isArray(i.settings))return[];let e=i.settings,t=[],r=0;for(let n=0,s=e.length;n<s;n++){let o=e[n];if(!o.settings)continue;let c;if(typeof o.scope=="string"){let h=o.scope;h=h.replace(/^[,]+/,""),h=h.replace(/[,]+$/,""),c=h.split(",")}else Array.isArray(o.scope)?c=o.scope:c=[""];let a=-1;if(typeof o.settings.fontStyle=="string"){a=0;let h=o.settings.fontStyle.split(" ");for(let p=0,d=h.length;p<d;p++)switch(h[p]){case"italic":a=a|1;break;case"bold":a=a|2;break;case"underline":a=a|4;break;case"strikethrough":a=a|8;break}}let u=null;typeof o.settings.foreground=="string"&&Qe(o.settings.foreground)&&(u=o.settings.foreground);let m=null;typeof o.settings.background=="string"&&Qe(o.settings.background)&&(m=o.settings.background);for(let h=0,p=c.length;h<p;h++){let f=c[h].trim().split(" "),T=f[f.length-1],E=null;f.length>1&&(E=f.slice(0,f.length-1),E.reverse()),t[r++]=new Sr(T,E,n,a,u,m)}}return t}var Sr=class{constructor(i,e,t,r,n,s){this.scope=i,this.parentScopes=e,this.index=t,this.fontStyle=r,this.foreground=n,this.background=s}},M=(i=>(i[i.NotSet=-1]="NotSet",i[i.None=0]="None",i[i.Italic=1]="Italic",i[i.Bold=2]="Bold",i[i.Underline=4]="Underline",i[i.Strikethrough=8]="Strikethrough",i))(M||{});function Or(i,e){i.sort((a,u)=>{let m=Et(a.scope,u.scope);return m!==0||(m=yt(a.parentScopes,u.parentScopes),m!==0)?m:a.index-u.index});let t=0,r="#000000",n="#ffffff";for(;i.length>=1&&i[0].scope==="";){let a=i.shift();a.fontStyle!==-1&&(t=a.fontStyle),a.foreground!==null&&(r=a.foreground),a.background!==null&&(n=a.background)}let s=new wr(e),o=new vt(t,s.getId(r),s.getId(n)),c=new kr(new Ce(0,null,-1,0,0),[]);for(let a=0,u=i.length;a<u;a++){let m=i[a];c.insert(0,m.scope,m.parentScopes,m.fontStyle,s.getId(m.foreground),s.getId(m.background))}return new le(s,o,c)}var wr=class{constructor(i){_(this,"_isFrozen");_(this,"_lastColorId");_(this,"_id2color");_(this,"_color2id");if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(i)){this._isFrozen=!0;for(let e=0,t=i.length;e<t;e++)this._color2id[i[e]]=e,this._id2color[e]=i[e]}else this._isFrozen=!1}getId(i){if(i===null)return 0;i=i.toUpperCase();let e=this._color2id[i];if(e)return e;if(this._isFrozen)throw new Error(`Missing color in color map - ${i}`);return e=++this._lastColorId,this._color2id[i]=e,this._id2color[e]=i,e}getColorMap(){return this._id2color.slice(0)}},Cr=Object.freeze([]),Ce=class At{constructor(e,t,r,n,s){_(this,"scopeDepth");_(this,"parentScopes");_(this,"fontStyle");_(this,"foreground");_(this,"background");this.scopeDepth=e,this.parentScopes=t||Cr,this.fontStyle=r,this.foreground=n,this.background=s}clone(){return new At(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(e){let t=[];for(let r=0,n=e.length;r<n;r++)t[r]=e[r].clone();return t}acceptOverwrite(e,t,r,n){this.scopeDepth>e?console.log("how did this happen?"):this.scopeDepth=e,t!==-1&&(this.fontStyle=t),r!==0&&(this.foreground=r),n!==0&&(this.background=n)}},kr=class ke{constructor(e,t=[],r={}){_(this,"_rulesWithParentScopes");this._mainRule=e,this._children=r,this._rulesWithParentScopes=t}static _cmpBySpecificity(e,t){if(e.scopeDepth!==t.scopeDepth)return t.scopeDepth-e.scopeDepth;let r=0,n=0;for(;e.parentScopes[r]===">"&&r++,t.parentScopes[n]===">"&&n++,!(r>=e.parentScopes.length||n>=t.parentScopes.length);){const s=t.parentScopes[n].length-e.parentScopes[r].length;if(s!==0)return s;r++,n++}return t.parentScopes.length-e.parentScopes.length}match(e){if(e!==""){let r=e.indexOf("."),n,s;if(r===-1?(n=e,s=""):(n=e.substring(0,r),s=e.substring(r+1)),this._children.hasOwnProperty(n))return this._children[n].match(s)}const t=this._rulesWithParentScopes.concat(this._mainRule);return t.sort(ke._cmpBySpecificity),t}insert(e,t,r,n,s,o){if(t===""){this._doInsertHere(e,r,n,s,o);return}let c=t.indexOf("."),a,u;c===-1?(a=t,u=""):(a=t.substring(0,c),u=t.substring(c+1));let m;this._children.hasOwnProperty(a)?m=this._children[a]:(m=new ke(this._mainRule.clone(),Ce.cloneArr(this._rulesWithParentScopes)),this._children[a]=m),m.insert(e+1,u,r,n,s,o)}_doInsertHere(e,t,r,n,s){if(t===null){this._mainRule.acceptOverwrite(e,r,n,s);return}for(let o=0,c=this._rulesWithParentScopes.length;o<c;o++){let a=this._rulesWithParentScopes[o];if(yt(a.parentScopes,t)===0){a.acceptOverwrite(e,r,n,s);return}}r===-1&&(r=this._mainRule.fontStyle),n===0&&(n=this._mainRule.foreground),s===0&&(s=this._mainRule.background),this._rulesWithParentScopes.push(new Ce(e,t,r,n,s))}},H=class C{static toBinaryStr(e){return e.toString(2).padStart(32,"0")}static print(e){const t=C.getLanguageId(e),r=C.getTokenType(e),n=C.getFontStyle(e),s=C.getForeground(e),o=C.getBackground(e);console.log({languageId:t,tokenType:r,fontStyle:n,foreground:s,background:o})}static getLanguageId(e){return(e&255)>>>0}static getTokenType(e){return(e&768)>>>8}static containsBalancedBrackets(e){return(e&1024)!==0}static getFontStyle(e){return(e&30720)>>>11}static getForeground(e){return(e&16744448)>>>15}static getBackground(e){return(e&4278190080)>>>24}static set(e,t,r,n,s,o,c){let a=C.getLanguageId(e),u=C.getTokenType(e),m=C.containsBalancedBrackets(e)?1:0,h=C.getFontStyle(e),p=C.getForeground(e),d=C.getBackground(e);return t!==0&&(a=t),r!==8&&(u=r),n!==null&&(m=n?1:0),s!==-1&&(h=s),o!==0&&(p=o),c!==0&&(d=c),(a<<0|u<<8|m<<10|h<<11|p<<15|d<<24)>>>0}};function ue(i,e){const t=[],r=Dr(i);let n=r.next();for(;n!==null;){let a=0;if(n.length===2&&n.charAt(1)===":"){switch(n.charAt(0)){case"R":a=1;break;case"L":a=-1;break;default:console.log(`Unknown priority ${n} in scope selector`)}n=r.next()}let u=o();if(t.push({matcher:u,priority:a}),n!==",")break;n=r.next()}return t;function s(){if(n==="-"){n=r.next();const a=s();return u=>!!a&&!a(u)}if(n==="("){n=r.next();const a=c();return n===")"&&(n=r.next()),a}if(Xe(n)){const a=[];do a.push(n),n=r.next();while(Xe(n));return u=>e(a,u)}return null}function o(){const a=[];let u=s();for(;u;)a.push(u),u=s();return m=>a.every(h=>h(m))}function c(){const a=[];let u=o();for(;u&&(a.push(u),n==="|"||n===",");){do n=r.next();while(n==="|"||n===",");u=o()}return m=>a.some(h=>h(m))}}function Xe(i){return!!i&&!!i.match(/[\w\.:]+/)}function Dr(i){let e=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g,t=e.exec(i);return{next:()=>{if(!t)return null;const r=t[0];return t=e.exec(i),r}}}function Lt(i){typeof i.dispose=="function"&&i.dispose()}var Q=class{constructor(i){this.scopeName=i}toKey(){return this.scopeName}},Nr=class{constructor(i,e){this.scopeName=i,this.ruleName=e}toKey(){return`${this.scopeName}#${this.ruleName}`}},Vr=class{constructor(){_(this,"_references",[]);_(this,"_seenReferenceKeys",new Set);_(this,"visitedRule",new Set)}get references(){return this._references}add(i){const e=i.toKey();this._seenReferenceKeys.has(e)||(this._seenReferenceKeys.add(e),this._references.push(i))}},xr=class{constructor(i,e){_(this,"seenFullScopeRequests",new Set);_(this,"seenPartialScopeRequests",new Set);_(this,"Q");this.repo=i,this.initialScopeName=e,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new Q(this.initialScopeName)]}processQueue(){const i=this.Q;this.Q=[];const e=new Vr;for(const t of i)Gr(t,this.initialScopeName,this.repo,e);for(const t of e.references)if(t instanceof Q){if(this.seenFullScopeRequests.has(t.scopeName))continue;this.seenFullScopeRequests.add(t.scopeName),this.Q.push(t)}else{if(this.seenFullScopeRequests.has(t.scopeName)||this.seenPartialScopeRequests.has(t.toKey()))continue;this.seenPartialScopeRequests.add(t.toKey()),this.Q.push(t)}}};function Gr(i,e,t,r){const n=t.lookup(i.scopeName);if(!n){if(i.scopeName===e)throw new Error(`No grammar provided for <${e}>`);return}const s=t.lookup(e);i instanceof Q?ce({baseGrammar:s,selfGrammar:n},r):De(i.ruleName,{baseGrammar:s,selfGrammar:n,repository:n.repository},r);const o=t.injections(i.scopeName);if(o)for(const c of o)r.add(new Q(c))}function De(i,e,t){if(e.repository&&e.repository[i]){const r=e.repository[i];me([r],e,t)}}function ce(i,e){i.selfGrammar.patterns&&Array.isArray(i.selfGrammar.patterns)&&me(i.selfGrammar.patterns,{...i,repository:i.selfGrammar.repository},e),i.selfGrammar.injections&&me(Object.values(i.selfGrammar.injections),{...i,repository:i.selfGrammar.repository},e)}function me(i,e,t){for(const r of i){if(t.visitedRule.has(r))continue;t.visitedRule.add(r);const n=r.repository?ft({},e.repository,r.repository):e.repository;Array.isArray(r.patterns)&&me(r.patterns,{...e,repository:n},t);const s=r.include;if(!s)continue;const o=Pt(s);switch(o.kind){case 0:ce({...e,selfGrammar:e.baseGrammar},t);break;case 1:ce(e,t);break;case 2:De(o.ruleName,{...e,repository:n},t);break;case 3:case 4:const c=o.scopeName===e.selfGrammar.scopeName?e.selfGrammar:o.scopeName===e.baseGrammar.scopeName?e.baseGrammar:void 0;if(c){const a={baseGrammar:e.baseGrammar,selfGrammar:c,repository:n};o.kind===4?De(o.ruleName,a,t):ce(a,t)}else o.kind===4?t.add(new Nr(o.scopeName,o.ruleName)):t.add(new Q(o.scopeName));break}}}var Mr=class{constructor(){_(this,"kind",0)}},Br=class{constructor(){_(this,"kind",1)}},jr=class{constructor(i){_(this,"kind",2);this.ruleName=i}},$r=class{constructor(i){_(this,"kind",3);this.scopeName=i}},Wr=class{constructor(i,e){_(this,"kind",4);this.scopeName=i,this.ruleName=e}};function Pt(i){if(i==="$base")return new Mr;if(i==="$self")return new Br;const e=i.indexOf("#");if(e===-1)return new $r(i);if(e===0)return new jr(i.substring(1));{const t=i.substring(0,e),r=i.substring(e+1);return new Wr(t,r)}}var Ur=/\\(\d+)/,Ye=/\\(\d+)/g,Hr=-1,bt=-2;var te=class{constructor(i,e,t,r){_(this,"$location");_(this,"id");_(this,"_nameIsCapturing");_(this,"_name");_(this,"_contentNameIsCapturing");_(this,"_contentName");this.$location=i,this.id=e,this._name=t||null,this._nameIsCapturing=ne.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=ne.hasCaptures(this._contentName)}get debugName(){const i=this.$location?`${gt(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${i}`}getName(i,e){return!this._nameIsCapturing||this._name===null||i===null||e===null?this._name:ne.replaceCaptures(this._name,i,e)}getContentName(i,e){return!this._contentNameIsCapturing||this._contentName===null?this._contentName:ne.replaceCaptures(this._contentName,i,e)}},Fr=class extends te{constructor(e,t,r,n,s){super(e,t,r,n);_(this,"retokenizeCapturedWithRuleId");this.retokenizeCapturedWithRuleId=s}dispose(){}collectPatterns(e,t){throw new Error("Not supported!")}compile(e,t){throw new Error("Not supported!")}compileAG(e,t,r,n){throw new Error("Not supported!")}},qr=class extends te{constructor(e,t,r,n,s){super(e,t,r,null);_(this,"_match");_(this,"captures");_(this,"_cachedCompiledPatterns");this._match=new X(n,this.id),this.captures=s,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(e,t){t.push(this._match)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,r,n){return this._getCachedCompiledPatterns(e).compileAG(e,r,n)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new Y,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},Ze=class extends te{constructor(e,t,r,n,s){super(e,t,r,n);_(this,"hasMissingPatterns");_(this,"patterns");_(this,"_cachedCompiledPatterns");this.patterns=s.patterns,this.hasMissingPatterns=s.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(e,t){for(const r of this.patterns)e.getRule(r).collectPatterns(e,t)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,r,n){return this._getCachedCompiledPatterns(e).compileAG(e,r,n)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new Y,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},Ne=class extends te{constructor(e,t,r,n,s,o,c,a,u,m){super(e,t,r,n);_(this,"_begin");_(this,"beginCaptures");_(this,"_end");_(this,"endHasBackReferences");_(this,"endCaptures");_(this,"applyEndPatternLast");_(this,"hasMissingPatterns");_(this,"patterns");_(this,"_cachedCompiledPatterns");this._begin=new X(s,this.id),this.beginCaptures=o,this._end=new X(c||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=a,this.applyEndPatternLast=u||!1,this.patterns=m.patterns,this.hasMissingPatterns=m.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(e,t){return this._end.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e,t).compile(e)}compileAG(e,t,r,n){return this._getCachedCompiledPatterns(e,t).compileAG(e,r,n)}_getCachedCompiledPatterns(e,t){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new Y;for(const r of this.patterns)e.getRule(r).collectPatterns(e,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,t):this._cachedCompiledPatterns.setSource(0,t)),this._cachedCompiledPatterns}},he=class extends te{constructor(e,t,r,n,s,o,c,a,u){super(e,t,r,n);_(this,"_begin");_(this,"beginCaptures");_(this,"whileCaptures");_(this,"_while");_(this,"whileHasBackReferences");_(this,"hasMissingPatterns");_(this,"patterns");_(this,"_cachedCompiledPatterns");_(this,"_cachedCompiledWhilePatterns");this._begin=new X(s,this.id),this.beginCaptures=o,this.whileCaptures=a,this._while=new X(c,bt),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=u.patterns,this.hasMissingPatterns=u.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(e,t){return this._while.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,r,n){return this._getCachedCompiledPatterns(e).compileAG(e,r,n)}_getCachedCompiledPatterns(e){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new Y;for(const t of this.patterns)e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns)}return this._cachedCompiledPatterns}compileWhile(e,t){return this._getCachedCompiledWhilePatterns(e,t).compile(e)}compileWhileAG(e,t,r,n){return this._getCachedCompiledWhilePatterns(e,t).compileAG(e,r,n)}_getCachedCompiledWhilePatterns(e,t){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new Y,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,t||"￿"),this._cachedCompiledWhilePatterns}},It=class S{static createCaptureRule(e,t,r,n,s){return e.registerRule(o=>new Fr(t,o,r,n,s))}static getCompiledRuleId(e,t,r){return e.id||t.registerRule(n=>{if(e.id=n,e.match)return new qr(e.$vscodeTextmateLocation,e.id,e.name,e.match,S._compileCaptures(e.captures,t,r));if(typeof e.begin>"u"){e.repository&&(r=ft({},r,e.repository));let s=e.patterns;return typeof s>"u"&&e.include&&(s=[{include:e.include}]),new Ze(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,S._compilePatterns(s,t,r))}return e.while?new he(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,e.begin,S._compileCaptures(e.beginCaptures||e.captures,t,r),e.while,S._compileCaptures(e.whileCaptures||e.captures,t,r),S._compilePatterns(e.patterns,t,r)):new Ne(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,e.begin,S._compileCaptures(e.beginCaptures||e.captures,t,r),e.end,S._compileCaptures(e.endCaptures||e.captures,t,r),e.applyEndPatternLast,S._compilePatterns(e.patterns,t,r))}),e.id}static _compileCaptures(e,t,r){let n=[];if(e){let s=0;for(const o in e){if(o==="$vscodeTextmateLocation")continue;const c=parseInt(o,10);c>s&&(s=c)}for(let o=0;o<=s;o++)n[o]=null;for(const o in e){if(o==="$vscodeTextmateLocation")continue;const c=parseInt(o,10);let a=0;e[o].patterns&&(a=S.getCompiledRuleId(e[o],t,r)),n[c]=S.createCaptureRule(t,e[o].$vscodeTextmateLocation,e[o].name,e[o].contentName,a)}}return n}static _compilePatterns(e,t,r){let n=[];if(e)for(let s=0,o=e.length;s<o;s++){const c=e[s];let a=-1;if(c.include){const u=Pt(c.include);switch(u.kind){case 0:case 1:a=S.getCompiledRuleId(r[c.include],t,r);break;case 2:let m=r[u.ruleName];m&&(a=S.getCompiledRuleId(m,t,r));break;case 3:case 4:const h=u.scopeName,p=u.kind===4?u.ruleName:null,d=t.getExternalGrammar(h,r);if(d)if(p){let f=d.repository[p];f&&(a=S.getCompiledRuleId(f,t,d.repository))}else a=S.getCompiledRuleId(d.repository.$self,t,d.repository);break}}else a=S.getCompiledRuleId(c,t,r);if(a!==-1){const u=t.getRule(a);let m=!1;if((u instanceof Ze||u instanceof Ne||u instanceof he)&&u.hasMissingPatterns&&u.patterns.length===0&&(m=!0),m)continue;n.push(a)}}return{patterns:n,hasMissingPatterns:(e?e.length:0)!==n.length}}},X=class St{constructor(e,t){_(this,"source");_(this,"ruleId");_(this,"hasAnchor");_(this,"hasBackReferences");_(this,"_anchorCache");if(e&&typeof e=="string"){const r=e.length;let n=0,s=[],o=!1;for(let c=0;c<r;c++)if(e.charAt(c)==="\\"&&c+1<r){const u=e.charAt(c+1);u==="z"?(s.push(e.substring(n,c)),s.push("$(?!\\n)(?<!\\n)"),n=c+2):(u==="A"||u==="G")&&(o=!0),c++}this.hasAnchor=o,n===0?this.source=e:(s.push(e.substring(n,r)),this.source=s.join(""))}else this.hasAnchor=!1,this.source=e;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=t,typeof this.source=="string"?this.hasBackReferences=Ur.test(this.source):this.hasBackReferences=!1}clone(){return new St(this.source,this.ruleId)}setSource(e){this.source!==e&&(this.source=e,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(e,t){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let r=t.map(n=>e.substring(n.start,n.end));return Ye.lastIndex=0,this.source.replace(Ye,(n,s)=>Rt(r[parseInt(s,10)]||""))}_buildAnchorCache(){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let e=[],t=[],r=[],n=[],s,o,c,a;for(s=0,o=this.source.length;s<o;s++)c=this.source.charAt(s),e[s]=c,t[s]=c,r[s]=c,n[s]=c,c==="\\"&&s+1<o&&(a=this.source.charAt(s+1),a==="A"?(e[s+1]="￿",t[s+1]="￿",r[s+1]="A",n[s+1]="A"):a==="G"?(e[s+1]="￿",t[s+1]="G",r[s+1]="￿",n[s+1]="G"):(e[s+1]=a,t[s+1]=a,r[s+1]=a,n[s+1]=a),s++);return{A0_G0:e.join(""),A0_G1:t.join(""),A1_G0:r.join(""),A1_G1:n.join("")}}resolveAnchors(e,t){return!this.hasAnchor||!this._anchorCache||typeof this.source!="string"?this.source:e?t?this._anchorCache.A1_G1:this._anchorCache.A1_G0:t?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},Y=class{constructor(){_(this,"_items");_(this,"_hasAnchors");_(this,"_cached");_(this,"_anchorCache");this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(i){this._items.push(i),this._hasAnchors=this._hasAnchors||i.hasAnchor}unshift(i){this._items.unshift(i),this._hasAnchors=this._hasAnchors||i.hasAnchor}length(){return this._items.length}setSource(i,e){this._items[i].source!==e&&(this._disposeCaches(),this._items[i].setSource(e))}compile(i){if(!this._cached){let e=this._items.map(t=>t.source);this._cached=new et(i,e,this._items.map(t=>t.ruleId))}return this._cached}compileAG(i,e,t){return this._hasAnchors?e?t?(this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(i,e,t)),this._anchorCache.A1_G1):(this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(i,e,t)),this._anchorCache.A1_G0):t?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(i,e,t)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(i,e,t)),this._anchorCache.A0_G0):this.compile(i)}_resolveAnchors(i,e,t){let r=this._items.map(n=>n.resolveAnchors(e,t));return new et(i,r,this._items.map(n=>n.ruleId))}},et=class{constructor(i,e,t){_(this,"scanner");this.regExps=e,this.rules=t,this.scanner=i.createOnigScanner(e)}dispose(){typeof this.scanner.dispose=="function"&&this.scanner.dispose()}toString(){const i=[];for(let e=0,t=this.rules.length;e<t;e++)i.push("   - "+this.rules[e]+": "+this.regExps[e]);return i.join(`
`)}findNextMatchSync(i,e,t){const r=this.scanner.findNextMatchSync(i,e,t);return r?{ruleId:this.rules[r.index],captureIndices:r.captureIndices}:null}},Ie=class{constructor(i,e){this.languageId=i,this.tokenType=e}},G,zr=(G=class{constructor(e,t){_(this,"_defaultAttributes");_(this,"_embeddedLanguagesMatcher");_(this,"_getBasicScopeAttributes",new Tt(e=>{const t=this._scopeToLanguage(e),r=this._toStandardTokenType(e);return new Ie(t,r)}));this._defaultAttributes=new Ie(e,8),this._embeddedLanguagesMatcher=new Jr(Object.entries(t||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(e){return e===null?G._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(e)}_scopeToLanguage(e){return this._embeddedLanguagesMatcher.match(e)||0}_toStandardTokenType(e){const t=e.match(G.STANDARD_TOKEN_TYPE_REGEXP);if(!t)return 8;switch(t[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw new Error("Unexpected match for standard token type!")}},_(G,"_NULL_SCOPE_METADATA",new Ie(0,0)),_(G,"STANDARD_TOKEN_TYPE_REGEXP",/\b(comment|string|regex|meta\.embedded)\b/),G),Jr=class{constructor(i){_(this,"values");_(this,"scopesRegExp");if(i.length===0)this.values=null,this.scopesRegExp=null;else{this.values=new Map(i);const e=i.map(([t,r])=>Rt(t));e.sort(),e.reverse(),this.scopesRegExp=new RegExp(`^((${e.join(")|(")}))($|\\.)`,"")}}match(i){if(!this.scopesRegExp)return;const e=i.match(this.scopesRegExp);if(e)return this.values.get(e[1])}},tt=class{constructor(i,e){this.stack=i,this.stoppedEarly=e}};function Ot(i,e,t,r,n,s,o,c){const a=e.content.length;let u=!1,m=-1;if(o){const d=Kr(i,e,t,r,n,s);n=d.stack,r=d.linePos,t=d.isFirstLine,m=d.anchorPosition}const h=Date.now();for(;!u;){if(c!==0&&Date.now()-h>c)return new tt(n,!0);p()}return new tt(n,!1);function p(){const d=Qr(i,e,t,r,n,m);if(!d){s.produce(n,a),u=!0;return}const f=d.captureIndices,T=d.matchedRuleId,E=f&&f.length>0?f[0].end>r:!1;if(T===Hr){const g=n.getRule(i);s.produce(n,f[0].start),n=n.withContentNameScopesList(n.nameScopesList),J(i,e,t,n,s,g.endCaptures,f),s.produce(n,f[0].end);const y=n;if(n=n.parent,m=y.getAnchorPos(),!E&&y.getEnterPos()===r){n=y,s.produce(n,a),u=!0;return}}else{const g=i.getRule(T);s.produce(n,f[0].start);const y=n,R=g.getName(e.content,f),v=n.contentNameScopesList.pushAttributed(R,i);if(n=n.push(T,r,m,f[0].end===a,null,v,v),g instanceof Ne){const A=g;J(i,e,t,n,s,A.beginCaptures,f),s.produce(n,f[0].end),m=f[0].end;const w=A.getContentName(e.content,f),L=v.pushAttributed(w,i);if(n=n.withContentNameScopesList(L),A.endHasBackReferences&&(n=n.withEndRule(A.getEndWithResolvedBackReferences(e.content,f))),!E&&y.hasSameRuleAs(n)){n=n.pop(),s.produce(n,a),u=!0;return}}else if(g instanceof he){const A=g;J(i,e,t,n,s,A.beginCaptures,f),s.produce(n,f[0].end),m=f[0].end;const w=A.getContentName(e.content,f),L=v.pushAttributed(w,i);if(n=n.withContentNameScopesList(L),A.whileHasBackReferences&&(n=n.withEndRule(A.getWhileWithResolvedBackReferences(e.content,f))),!E&&y.hasSameRuleAs(n)){n=n.pop(),s.produce(n,a),u=!0;return}}else if(J(i,e,t,n,s,g.captures,f),s.produce(n,f[0].end),n=n.pop(),!E){n=n.safePop(),s.produce(n,a),u=!0;return}}f[0].end>r&&(r=f[0].end,t=!1)}}function Kr(i,e,t,r,n,s){let o=n.beginRuleCapturedEOL?0:-1;const c=[];for(let a=n;a;a=a.pop()){const u=a.getRule(i);u instanceof he&&c.push({rule:u,stack:a})}for(let a=c.pop();a;a=c.pop()){const{ruleScanner:u,findOptions:m}=Zr(a.rule,i,a.stack.endRule,t,r===o),h=u.findNextMatchSync(e,r,m);if(h){if(h.ruleId!==bt){n=a.stack.pop();break}h.captureIndices&&h.captureIndices.length&&(s.produce(a.stack,h.captureIndices[0].start),J(i,e,t,a.stack,s,a.rule.whileCaptures,h.captureIndices),s.produce(a.stack,h.captureIndices[0].end),o=h.captureIndices[0].end,h.captureIndices[0].end>r&&(r=h.captureIndices[0].end,t=!1))}else{n=a.stack.pop();break}}return{stack:n,linePos:r,anchorPosition:o,isFirstLine:t}}function Qr(i,e,t,r,n,s){const o=Xr(i,e,t,r,n,s),c=i.getInjections();if(c.length===0)return o;const a=Yr(c,i,e,t,r,n,s);if(!a)return o;if(!o)return a;const u=o.captureIndices[0].start,m=a.captureIndices[0].start;return m<u||a.priorityMatch&&m===u?a:o}function Xr(i,e,t,r,n,s){const o=n.getRule(i),{ruleScanner:c,findOptions:a}=wt(o,i,n.endRule,t,r===s),u=c.findNextMatchSync(e,r,a);return u?{captureIndices:u.captureIndices,matchedRuleId:u.ruleId}:null}function Yr(i,e,t,r,n,s,o){let c=Number.MAX_VALUE,a=null,u,m=0;const h=s.contentNameScopesList.getScopeNames();for(let p=0,d=i.length;p<d;p++){const f=i[p];if(!f.matcher(h))continue;const T=e.getRule(f.ruleId),{ruleScanner:E,findOptions:g}=wt(T,e,null,r,n===o),y=E.findNextMatchSync(t,n,g);if(!y)continue;const R=y.captureIndices[0].start;if(!(R>=c)&&(c=R,a=y.captureIndices,u=y.ruleId,m=f.priority,c===n))break}return a?{priorityMatch:m===-1,captureIndices:a,matchedRuleId:u}:null}function wt(i,e,t,r,n){return{ruleScanner:i.compileAG(e,t,r,n),findOptions:0}}function Zr(i,e,t,r,n){return{ruleScanner:i.compileWhileAG(e,t,r,n),findOptions:0}}function J(i,e,t,r,n,s,o){if(s.length===0)return;const c=e.content,a=Math.min(s.length,o.length),u=[],m=o[0].end;for(let h=0;h<a;h++){const p=s[h];if(p===null)continue;const d=o[h];if(d.length===0)continue;if(d.start>m)break;for(;u.length>0&&u[u.length-1].endPos<=d.start;)n.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop();if(u.length>0?n.produceFromScopes(u[u.length-1].scopes,d.start):n.produce(r,d.start),p.retokenizeCapturedWithRuleId){const T=p.getName(c,o),E=r.contentNameScopesList.pushAttributed(T,i),g=p.getContentName(c,o),y=E.pushAttributed(g,i),R=r.push(p.retokenizeCapturedWithRuleId,d.start,-1,!1,null,E,y),v=i.createOnigString(c.substring(0,d.end));Ot(i,v,t&&d.start===0,d.start,R,n,!1,0),Lt(v);continue}const f=p.getName(c,o);if(f!==null){const E=(u.length>0?u[u.length-1].scopes:r.contentNameScopesList).pushAttributed(f,i);u.push(new ei(E,d.end))}}for(;u.length>0;)n.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop()}var ei=class{constructor(i,e){_(this,"scopes");_(this,"endPos");this.scopes=i,this.endPos=e}};function ti(i,e,t,r,n,s,o,c){return new ii(i,e,t,r,n,s,o,c)}function rt(i,e,t,r,n){const s=ue(e,de),o=It.getCompiledRuleId(t,r,n.repository);for(const c of s)i.push({debugSelector:e,matcher:c.matcher,ruleId:o,grammar:n,priority:c.priority})}function de(i,e){if(e.length<i.length)return!1;let t=0;return i.every(r=>{for(let n=t;n<e.length;n++)if(ri(e[n],r))return t=n+1,!0;return!1})}function ri(i,e){if(!i)return!1;if(i===e)return!0;const t=e.length;return i.length>t&&i.substr(0,t)===e&&i[t]==="."}var ii=class{constructor(i,e,t,r,n,s,o,c){_(this,"_rootId");_(this,"_lastRuleId");_(this,"_ruleId2desc");_(this,"_includedGrammars");_(this,"_grammarRepository");_(this,"_grammar");_(this,"_injections");_(this,"_basicScopeAttributesProvider");_(this,"_tokenTypeMatchers");if(this._rootScopeName=i,this.balancedBracketSelectors=s,this._onigLib=c,this._basicScopeAttributesProvider=new zr(t,r),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=o,this._grammar=it(e,null),this._injections=null,this._tokenTypeMatchers=[],n)for(const a of Object.keys(n)){const u=ue(a,de);for(const m of u)this._tokenTypeMatchers.push({matcher:m.matcher,type:n[a]})}}get themeProvider(){return this._grammarRepository}dispose(){for(const i of this._ruleId2desc)i&&i.dispose()}createOnigScanner(i){return this._onigLib.createOnigScanner(i)}createOnigString(i){return this._onigLib.createOnigString(i)}getMetadataForScope(i){return this._basicScopeAttributesProvider.getBasicScopeAttributes(i)}_collectInjections(){const i={lookup:n=>n===this._rootScopeName?this._grammar:this.getExternalGrammar(n),injections:n=>this._grammarRepository.injections(n)},e=[],t=this._rootScopeName,r=i.lookup(t);if(r){const n=r.injections;if(n)for(let o in n)rt(e,o,n[o],this,r);const s=this._grammarRepository.injections(t);s&&s.forEach(o=>{const c=this.getExternalGrammar(o);if(c){const a=c.injectionSelector;a&&rt(e,a,c,this,c)}})}return e.sort((n,s)=>n.priority-s.priority),e}getInjections(){return this._injections===null&&(this._injections=this._collectInjections()),this._injections}registerRule(i){const e=++this._lastRuleId,t=i(e);return this._ruleId2desc[e]=t,t}getRule(i){return this._ruleId2desc[i]}getExternalGrammar(i,e){if(this._includedGrammars[i])return this._includedGrammars[i];if(this._grammarRepository){const t=this._grammarRepository.lookup(i);if(t)return this._includedGrammars[i]=it(t,e&&e.$base),this._includedGrammars[i]}}tokenizeLine(i,e,t=0){const r=this._tokenize(i,e,!1,t);return{tokens:r.lineTokens.getResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}tokenizeLine2(i,e,t=0){const r=this._tokenize(i,e,!0,t);return{tokens:r.lineTokens.getBinaryResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}_tokenize(i,e,t,r){this._rootId===-1&&(this._rootId=It.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections());let n;if(!e||e===Ve.NULL){n=!0;const u=this._basicScopeAttributesProvider.getDefaultAttributes(),m=this.themeProvider.getDefaults(),h=H.set(0,u.languageId,u.tokenType,null,m.fontStyle,m.foregroundId,m.backgroundId),p=this.getRule(this._rootId).getName(null,null);let d;p?d=K.createRootAndLookUpScopeName(p,h,this):d=K.createRoot("unknown",h),e=new Ve(null,this._rootId,-1,-1,!1,null,d,d)}else n=!1,e.reset();i=i+`
`;const s=this.createOnigString(i),o=s.content.length,c=new si(t,i,this._tokenTypeMatchers,this.balancedBracketSelectors),a=Ot(this,s,n,0,e,c,!0,r);return Lt(s),{lineLength:o,lineTokens:c,ruleStack:a.stack,stoppedEarly:a.stoppedEarly}}};function it(i,e){return i=vr(i),i.repository=i.repository||{},i.repository.$self={$vscodeTextmateLocation:i.$vscodeTextmateLocation,patterns:i.patterns,name:i.scopeName},i.repository.$base=e||i.repository.$self,i}var K=class N{constructor(e,t,r){this.parent=e,this.scopePath=t,this.tokenAttributes=r}static fromExtension(e,t){let r=e,n=(e==null?void 0:e.scopePath)??null;for(const s of t)n=be.push(n,s.scopeNames),r=new N(r,n,s.encodedTokenAttributes);return r}static createRoot(e,t){return new N(null,new be(null,e),t)}static createRootAndLookUpScopeName(e,t,r){const n=r.getMetadataForScope(e),s=new be(null,e),o=r.themeProvider.themeMatch(s),c=N.mergeAttributes(t,n,o);return new N(null,s,c)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(e){return N.equals(this,e)}static equals(e,t){do{if(e===t||!e&&!t)return!0;if(!e||!t||e.scopeName!==t.scopeName||e.tokenAttributes!==t.tokenAttributes)return!1;e=e.parent,t=t.parent}while(!0)}static mergeAttributes(e,t,r){let n=-1,s=0,o=0;return r!==null&&(n=r.fontStyle,s=r.foregroundId,o=r.backgroundId),H.set(e,t.languageId,t.tokenType,null,n,s,o)}pushAttributed(e,t){if(e===null)return this;if(e.indexOf(" ")===-1)return N._pushAttributed(this,e,t);const r=e.split(/ /g);let n=this;for(const s of r)n=N._pushAttributed(n,s,t);return n}static _pushAttributed(e,t,r){const n=r.getMetadataForScope(t),s=e.scopePath.push(t),o=r.themeProvider.themeMatch(s),c=N.mergeAttributes(e.tokenAttributes,n,o);return new N(e,s,c)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(e){var n;const t=[];let r=this;for(;r&&r!==e;)t.push({encodedTokenAttributes:r.tokenAttributes,scopeNames:r.scopePath.getExtensionIfDefined(((n=r.parent)==null?void 0:n.scopePath)??null)}),r=r.parent;return r===e?t.reverse():void 0}},k,Ve=(k=class{constructor(e,t,r,n,s,o,c,a){_(this,"_stackElementBrand");_(this,"_enterPos");_(this,"_anchorPos");_(this,"depth");this.parent=e,this.ruleId=t,this.beginRuleCapturedEOL=s,this.endRule=o,this.nameScopesList=c,this.contentNameScopesList=a,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=r,this._anchorPos=n}equals(e){return e===null?!1:k._equals(this,e)}static _equals(e,t){return e===t?!0:this._structuralEquals(e,t)?K.equals(e.contentNameScopesList,t.contentNameScopesList):!1}static _structuralEquals(e,t){do{if(e===t||!e&&!t)return!0;if(!e||!t||e.depth!==t.depth||e.ruleId!==t.ruleId||e.endRule!==t.endRule)return!1;e=e.parent,t=t.parent}while(!0)}clone(){return this}static _reset(e){for(;e;)e._enterPos=-1,e._anchorPos=-1,e=e.parent}reset(){k._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(e,t,r,n,s,o,c){return new k(this,e,t,r,n,s,o,c)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(e){return e.getRule(this.ruleId)}toString(){const e=[];return this._writeString(e,0),"["+e.join(",")+"]"}_writeString(e,t){var r,n;return this.parent&&(t=this.parent._writeString(e,t)),e[t++]=`(${this.ruleId}, ${(r=this.nameScopesList)==null?void 0:r.toString()}, ${(n=this.contentNameScopesList)==null?void 0:n.toString()})`,t}withContentNameScopesList(e){return this.contentNameScopesList===e?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,e)}withEndRule(e){return this.endRule===e?this:new k(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,e,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(e){let t=this;for(;t&&t._enterPos===e._enterPos;){if(t.ruleId===e.ruleId)return!0;t=t.parent}return!1}toStateStackFrame(){var e,t,r;return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:((t=this.nameScopesList)==null?void 0:t.getExtensionIfDefined(((e=this.parent)==null?void 0:e.nameScopesList)??null))??[],contentNameScopesList:((r=this.contentNameScopesList)==null?void 0:r.getExtensionIfDefined(this.nameScopesList))??[]}}static pushFrame(e,t){const r=K.fromExtension((e==null?void 0:e.nameScopesList)??null,t.nameScopesList);return new k(e,t.ruleId,t.enterPos??-1,t.anchorPos??-1,t.beginRuleCapturedEOL,t.endRule,r,K.fromExtension(r,t.contentNameScopesList))}},_(k,"NULL",new k(null,0,0,0,!1,null,null,null)),k),ni=class{constructor(i,e){_(this,"balancedBracketScopes");_(this,"unbalancedBracketScopes");_(this,"allowAny",!1);this.balancedBracketScopes=i.flatMap(t=>t==="*"?(this.allowAny=!0,[]):ue(t,de).map(r=>r.matcher)),this.unbalancedBracketScopes=e.flatMap(t=>ue(t,de).map(r=>r.matcher))}get matchesAlways(){return this.allowAny&&this.unbalancedBracketScopes.length===0}get matchesNever(){return this.balancedBracketScopes.length===0&&!this.allowAny}match(i){for(const e of this.unbalancedBracketScopes)if(e(i))return!1;for(const e of this.balancedBracketScopes)if(e(i))return!0;return this.allowAny}},si=class{constructor(i,e,t,r){_(this,"_emitBinaryTokens");_(this,"_lineText");_(this,"_tokens");_(this,"_binaryTokens");_(this,"_lastTokenEndIndex");_(this,"_tokenTypeOverrides");this.balancedBracketSelectors=r,this._emitBinaryTokens=i,this._tokenTypeOverrides=t,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(i,e){this.produceFromScopes(i.contentNameScopesList,e)}produceFromScopes(i,e){var r;if(this._lastTokenEndIndex>=e)return;if(this._emitBinaryTokens){let n=(i==null?void 0:i.tokenAttributes)??0,s=!1;if((r=this.balancedBracketSelectors)!=null&&r.matchesAlways&&(s=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){const o=(i==null?void 0:i.getScopeNames())??[];for(const c of this._tokenTypeOverrides)c.matcher(o)&&(n=H.set(n,0,c.type,null,-1,0,0));this.balancedBracketSelectors&&(s=this.balancedBracketSelectors.match(o))}if(s&&(n=H.set(n,0,8,s,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===n){this._lastTokenEndIndex=e;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(n),this._lastTokenEndIndex=e;return}const t=(i==null?void 0:i.getScopeNames())??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:e,scopes:t}),this._lastTokenEndIndex=e}getResult(i,e){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===e-1&&this._tokens.pop(),this._tokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(i,e),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(i,e){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===e-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),this._binaryTokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(i,e),this._binaryTokens[this._binaryTokens.length-2]=0);const t=new Uint32Array(this._binaryTokens.length);for(let r=0,n=this._binaryTokens.length;r<n;r++)t[r]=this._binaryTokens[r];return t}},oi=class{constructor(i,e){_(this,"_grammars",new Map);_(this,"_rawGrammars",new Map);_(this,"_injectionGrammars",new Map);_(this,"_theme");this._onigLib=e,this._theme=i}dispose(){for(const i of this._grammars.values())i.dispose()}setTheme(i){this._theme=i}getColorMap(){return this._theme.getColorMap()}addGrammar(i,e){this._rawGrammars.set(i.scopeName,i),e&&this._injectionGrammars.set(i.scopeName,e)}lookup(i){return this._rawGrammars.get(i)}injections(i){return this._injectionGrammars.get(i)}getDefaults(){return this._theme.getDefaults()}themeMatch(i){return this._theme.match(i)}grammarForScopeName(i,e,t,r,n){if(!this._grammars.has(i)){let s=this._rawGrammars.get(i);if(!s)return null;this._grammars.set(i,ti(i,s,e,t,r,n,this,this._onigLib))}return this._grammars.get(i)}},ai=class{constructor(e){_(this,"_options");_(this,"_syncRegistry");_(this,"_ensureGrammarCache");this._options=e,this._syncRegistry=new oi(le.createFromRawTheme(e.theme,e.colorMap),e.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(e,t){this._syncRegistry.setTheme(le.createFromRawTheme(e,t))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(e,t,r){return this.loadGrammarWithConfiguration(e,t,{embeddedLanguages:r})}loadGrammarWithConfiguration(e,t,r){return this._loadGrammar(e,t,r.embeddedLanguages,r.tokenTypes,new ni(r.balancedBracketSelectors||[],r.unbalancedBracketSelectors||[]))}loadGrammar(e){return this._loadGrammar(e,0,null,null,null)}_loadGrammar(e,t,r,n,s){const o=new xr(this._syncRegistry,e);for(;o.Q.length>0;)o.Q.map(c=>this._loadSingleGrammar(c.scopeName)),o.processQueue();return this._grammarForScopeName(e,t,r,n,s)}_loadSingleGrammar(e){this._ensureGrammarCache.has(e)||(this._doLoadSingleGrammar(e),this._ensureGrammarCache.set(e,!0))}_doLoadSingleGrammar(e){const t=this._options.loadGrammar(e);if(t){const r=typeof this._options.getInjections=="function"?this._options.getInjections(e):void 0;this._syncRegistry.addGrammar(t,r)}}addGrammar(e,t=[],r=0,n=null){return this._syncRegistry.addGrammar(e,t),this._grammarForScopeName(e.scopeName,r,n)}_grammarForScopeName(e,t=0,r=null,n=null,s=null){return this._syncRegistry.grammarForScopeName(e,t,r,n,s)}},xe=Ve.NULL;const ci=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"],li=/["&'<>`]/g,ui=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,mi=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,hi=/[|\\{}()[\]^$+*?.]/g,nt=new WeakMap;function di(i,e){if(i=i.replace(e.subset?pi(e.subset):li,r),e.subset||e.escapeOnly)return i;return i.replace(ui,t).replace(mi,r);function t(n,s,o){return e.format((n.charCodeAt(0)-55296)*1024+n.charCodeAt(1)-56320+65536,o.charCodeAt(s+2),e)}function r(n,s,o){return e.format(n.charCodeAt(0),o.charCodeAt(s+1),e)}}function pi(i){let e=nt.get(i);return e||(e=_i(i),nt.set(i,e)),e}function _i(i){const e=[];let t=-1;for(;++t<i.length;)e.push(i[t].replace(hi,"\\$&"));return new RegExp("(?:"+e.join("|")+")","g")}const fi=/[\dA-Fa-f]/;function gi(i,e,t){const r="&#x"+i.toString(16).toUpperCase();return t&&e&&!fi.test(String.fromCharCode(e))?r:r+";"}const Ei=/\d/;function yi(i,e,t){const r="&#"+String(i);return t&&e&&!Ei.test(String.fromCharCode(e))?r:r+";"}const Ri=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],Se={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},Ti=["cent","copy","divide","gt","lt","not","para","times"],Ct={}.hasOwnProperty,Ge={};let se;for(se in Se)Ct.call(Se,se)&&(Ge[Se[se]]=se);const vi=/[^\dA-Za-z]/;function Ai(i,e,t,r){const n=String.fromCharCode(i);if(Ct.call(Ge,n)){const s=Ge[n],o="&"+s;return t&&Ri.includes(s)&&!Ti.includes(s)&&(!r||e&&e!==61&&vi.test(String.fromCharCode(e)))?o:o+";"}return""}function Li(i,e,t){let r=gi(i,e,t.omitOptionalSemicolons),n;if((t.useNamedReferences||t.useShortestReferences)&&(n=Ai(i,e,t.omitOptionalSemicolons,t.attribute)),(t.useShortestReferences||!n)&&t.useShortestReferences){const s=yi(i,e,t.omitOptionalSemicolons);s.length<r.length&&(r=s)}return n&&(!t.useShortestReferences||n.length<r.length)?n:r}function U(i,e){return di(i,Object.assign({format:Li},e))}const Pi=/^>|^->|<!--|-->|--!>|<!-$/g,bi=[">"],Ii=["<",">"];function Si(i,e,t,r){return r.settings.bogusComments?"<?"+U(i.value,Object.assign({},r.settings.characterReferences,{subset:bi}))+">":"<!--"+i.value.replace(Pi,n)+"-->";function n(s){return U(s,Object.assign({},r.settings.characterReferences,{subset:Ii}))}}function Oi(i,e,t,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"}const b=Dt(1),kt=Dt(-1),wi=[];function Dt(i){return e;function e(t,r,n){const s=t?t.children:wi;let o=(r||0)+i,c=s[o];if(!n)for(;c&&Me(c);)o+=i,c=s[o];return c}}const Ci={}.hasOwnProperty;function Nt(i){return e;function e(t,r,n){return Ci.call(i,t.tagName)&&i[t.tagName](t,r,n)}}const $e=Nt({body:Di,caption:Oe,colgroup:Oe,dd:Gi,dt:xi,head:Oe,html:ki,li:Vi,optgroup:Mi,option:Bi,p:Ni,rp:st,rt:st,tbody:$i,td:ot,tfoot:Wi,th:ot,thead:ji,tr:Ui});function Oe(i,e,t){const r=b(t,e,!0);return!r||r.type!=="comment"&&!(r.type==="text"&&Me(r.value.charAt(0)))}function ki(i,e,t){const r=b(t,e);return!r||r.type!=="comment"}function Di(i,e,t){const r=b(t,e);return!r||r.type!=="comment"}function Ni(i,e,t){const r=b(t,e);return r?r.type==="element"&&(r.tagName==="address"||r.tagName==="article"||r.tagName==="aside"||r.tagName==="blockquote"||r.tagName==="details"||r.tagName==="div"||r.tagName==="dl"||r.tagName==="fieldset"||r.tagName==="figcaption"||r.tagName==="figure"||r.tagName==="footer"||r.tagName==="form"||r.tagName==="h1"||r.tagName==="h2"||r.tagName==="h3"||r.tagName==="h4"||r.tagName==="h5"||r.tagName==="h6"||r.tagName==="header"||r.tagName==="hgroup"||r.tagName==="hr"||r.tagName==="main"||r.tagName==="menu"||r.tagName==="nav"||r.tagName==="ol"||r.tagName==="p"||r.tagName==="pre"||r.tagName==="section"||r.tagName==="table"||r.tagName==="ul"):!t||!(t.type==="element"&&(t.tagName==="a"||t.tagName==="audio"||t.tagName==="del"||t.tagName==="ins"||t.tagName==="map"||t.tagName==="noscript"||t.tagName==="video"))}function Vi(i,e,t){const r=b(t,e);return!r||r.type==="element"&&r.tagName==="li"}function xi(i,e,t){const r=b(t,e);return!!(r&&r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd"))}function Gi(i,e,t){const r=b(t,e);return!r||r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd")}function st(i,e,t){const r=b(t,e);return!r||r.type==="element"&&(r.tagName==="rp"||r.tagName==="rt")}function Mi(i,e,t){const r=b(t,e);return!r||r.type==="element"&&r.tagName==="optgroup"}function Bi(i,e,t){const r=b(t,e);return!r||r.type==="element"&&(r.tagName==="option"||r.tagName==="optgroup")}function ji(i,e,t){const r=b(t,e);return!!(r&&r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot"))}function $i(i,e,t){const r=b(t,e);return!r||r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot")}function Wi(i,e,t){return!b(t,e)}function Ui(i,e,t){const r=b(t,e);return!r||r.type==="element"&&r.tagName==="tr"}function ot(i,e,t){const r=b(t,e);return!r||r.type==="element"&&(r.tagName==="td"||r.tagName==="th")}const Hi=Nt({body:zi,colgroup:Ji,head:qi,html:Fi,tbody:Ki});function Fi(i){const e=b(i,-1);return!e||e.type!=="comment"}function qi(i){const e=new Set;for(const r of i.children)if(r.type==="element"&&(r.tagName==="base"||r.tagName==="title")){if(e.has(r.tagName))return!1;e.add(r.tagName)}const t=i.children[0];return!t||t.type==="element"}function zi(i){const e=b(i,-1,!0);return!e||e.type!=="comment"&&!(e.type==="text"&&Me(e.value.charAt(0)))&&!(e.type==="element"&&(e.tagName==="meta"||e.tagName==="link"||e.tagName==="script"||e.tagName==="style"||e.tagName==="template"))}function Ji(i,e,t){const r=kt(t,e),n=b(i,-1,!0);return t&&r&&r.type==="element"&&r.tagName==="colgroup"&&$e(r,t.children.indexOf(r),t)?!1:!!(n&&n.type==="element"&&n.tagName==="col")}function Ki(i,e,t){const r=kt(t,e),n=b(i,-1);return t&&r&&r.type==="element"&&(r.tagName==="thead"||r.tagName==="tbody")&&$e(r,t.children.indexOf(r),t)?!1:!!(n&&n.type==="element"&&n.tagName==="tr")}const oe={name:[[`	
\f\r &/=>`.split(""),`	
\f\r "&'/=>\``.split("")],[`\0	
\f\r "&'/<=>`.split(""),`\0	
\f\r "&'/<=>\``.split("")]],unquoted:[[`	
\f\r &>`.split(""),`\0	
\f\r "&'<=>\``.split("")],[`\0	
\f\r "&'<=>\``.split(""),`\0	
\f\r "&'<=>\``.split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]};function Qi(i,e,t,r){const n=r.schema,s=n.space==="svg"?!1:r.settings.omitOptionalTags;let o=n.space==="svg"?r.settings.closeEmptyElements:r.settings.voids.includes(i.tagName.toLowerCase());const c=[];let a;n.space==="html"&&i.tagName==="svg"&&(r.schema=dt);const u=Xi(r,i.properties),m=r.all(n.space==="html"&&i.tagName==="template"?i.content:i);return r.schema=n,m&&(o=!1),(u||!s||!Hi(i,e,t))&&(c.push("<",i.tagName,u?" "+u:""),o&&(n.space==="svg"||r.settings.closeSelfClosing)&&(a=u.charAt(u.length-1),(!r.settings.tightSelfClosing||a==="/"||a&&a!=='"'&&a!=="'")&&c.push(" "),c.push("/")),c.push(">")),c.push(m),!o&&(!s||!$e(i,e,t))&&c.push("</"+i.tagName+">"),c.join("")}function Xi(i,e){const t=[];let r=-1,n;if(e){for(n in e)if(e[n]!==null&&e[n]!==void 0){const s=Yi(i,n,e[n]);s&&t.push(s)}}for(;++r<t.length;){const s=i.settings.tightAttributes?t[r].charAt(t[r].length-1):void 0;r!==t.length-1&&s!=='"'&&s!=="'"&&(t[r]+=" ")}return t.join("")}function Yi(i,e,t){const r=Jt(i.schema,e),n=i.settings.allowParseErrors&&i.schema.space==="html"?0:1,s=i.settings.allowDangerousCharacters?0:1;let o=i.quote,c;if(r.overloadedBoolean&&(t===r.attribute||t==="")?t=!0:(r.boolean||r.overloadedBoolean)&&(typeof t!="string"||t===r.attribute||t==="")&&(t=!!t),t==null||t===!1||typeof t=="number"&&Number.isNaN(t))return"";const a=U(r.attribute,Object.assign({},i.settings.characterReferences,{subset:oe.name[n][s]}));return t===!0||(t=Array.isArray(t)?(r.commaSeparated?Kt:Qt)(t,{padLeft:!i.settings.tightCommaSeparatedLists}):String(t),i.settings.collapseEmptyAttributes&&!t)?a:(i.settings.preferUnquoted&&(c=U(t,Object.assign({},i.settings.characterReferences,{attribute:!0,subset:oe.unquoted[n][s]}))),c!==t&&(i.settings.quoteSmart&&Je(t,o)>Je(t,i.alternative)&&(o=i.alternative),c=o+U(t,Object.assign({},i.settings.characterReferences,{subset:(o==="'"?oe.single:oe.double)[n][s],attribute:!0}))+o),a+(c&&"="+c))}const Zi=["<","&"];function Vt(i,e,t,r){return t&&t.type==="element"&&(t.tagName==="script"||t.tagName==="style")?i.value:U(i.value,Object.assign({},r.settings.characterReferences,{subset:Zi}))}function en(i,e,t,r){return r.settings.allowDangerousHtml?i.value:Vt(i,e,t,r)}function tn(i,e,t,r){return r.all(i)}const rn=Yt("type",{invalid:nn,unknown:sn,handlers:{comment:Si,doctype:Oi,element:Qi,raw:en,root:tn,text:Vt}});function nn(i){throw new Error("Expected node, not `"+i+"`")}function sn(i){const e=i;throw new Error("Cannot compile unknown node `"+e.type+"`")}const on={},an={},cn=[];function ln(i,e){const t=on,r=t.quote||'"',n=r==='"'?"'":'"';if(r!=='"'&&r!=="'")throw new Error("Invalid quote `"+r+"`, expected `'` or `\"`");return{one:un,all:mn,settings:{omitOptionalTags:t.omitOptionalTags||!1,allowParseErrors:t.allowParseErrors||!1,allowDangerousCharacters:t.allowDangerousCharacters||!1,quoteSmart:t.quoteSmart||!1,preferUnquoted:t.preferUnquoted||!1,tightAttributes:t.tightAttributes||!1,upperDoctype:t.upperDoctype||!1,tightDoctype:t.tightDoctype||!1,bogusComments:t.bogusComments||!1,tightCommaSeparatedLists:t.tightCommaSeparatedLists||!1,tightSelfClosing:t.tightSelfClosing||!1,collapseEmptyAttributes:t.collapseEmptyAttributes||!1,allowDangerousHtml:t.allowDangerousHtml||!1,voids:t.voids||ci,characterReferences:t.characterReferences||an,closeSelfClosing:t.closeSelfClosing||!1,closeEmptyElements:t.closeEmptyElements||!1},schema:t.space==="svg"?dt:Xt,quote:r,alternative:n}.one(Array.isArray(i)?{type:"root",children:i}:i,void 0,void 0)}function un(i,e,t){return rn(i,e,t,this)}function mn(i){const e=[],t=i&&i.children||cn;let r=-1;for(;++r<t.length;)e[r]=this.one(t[r],r,i);return e.join("")}function hn(i){return Array.isArray(i)?i:[i]}function Re(i,e=!1){var s;const t=i.split(/(\r?\n)/g);let r=0;const n=[];for(let o=0;o<t.length;o+=2){const c=e?t[o]+(t[o+1]||""):t[o];n.push([c,r]),r+=t[o].length,r+=((s=t[o+1])==null?void 0:s.length)||0}return n}function We(i){return!i||["plaintext","txt","text","plain"].includes(i)}function xt(i){return i==="ansi"||We(i)}function Ue(i){return i==="none"}function Gt(i){return Ue(i)}function Mt(i,e){var r;if(!e)return i;i.properties||(i.properties={}),(r=i.properties).class||(r.class=[]),typeof i.properties.class=="string"&&(i.properties.class=i.properties.class.split(/\s+/g)),Array.isArray(i.properties.class)||(i.properties.class=[]);const t=Array.isArray(e)?e:e.split(/\s+/g);for(const n of t)n&&!i.properties.class.includes(n)&&i.properties.class.push(n);return i}function dn(i,e){let t=0;const r=[];for(const n of e)n>t&&r.push({...i,content:i.content.slice(t,n),offset:i.offset+t}),t=n;return t<i.content.length&&r.push({...i,content:i.content.slice(t),offset:i.offset+t}),r}function pn(i,e){const t=Array.from(e instanceof Set?e:new Set(e)).sort((r,n)=>r-n);return t.length?i.map(r=>r.flatMap(n=>{const s=t.filter(o=>n.offset<o&&o<n.offset+n.content.length).map(o=>o-n.offset).sort((o,c)=>o-c);return s.length?dn(n,s):n})):i}async function Bt(i){return Promise.resolve(typeof i=="function"?i():i).then(e=>e.default||e)}function pe(i,e){const t=typeof i=="string"?{}:{...i.colorReplacements},r=typeof i=="string"?i:i.name;for(const[n,s]of Object.entries((e==null?void 0:e.colorReplacements)||{}))typeof s=="string"?t[n]=s:n===r&&Object.assign(t,s);return t}function j(i,e){return i&&((e==null?void 0:e[i==null?void 0:i.toLowerCase()])||i)}function jt(i){const e={};return i.color&&(e.color=i.color),i.bgColor&&(e["background-color"]=i.bgColor),i.fontStyle&&(i.fontStyle&M.Italic&&(e["font-style"]="italic"),i.fontStyle&M.Bold&&(e["font-weight"]="bold"),i.fontStyle&M.Underline&&(e["text-decoration"]="underline")),e}function _n(i){return typeof i=="string"?i:Object.entries(i).map(([e,t])=>`${e}:${t}`).join(";")}function fn(i){const e=Re(i,!0).map(([n])=>n);function t(n){if(n===i.length)return{line:e.length-1,character:e[e.length-1].length};let s=n,o=0;for(const c of e){if(s<c.length)break;s-=c.length,o++}return{line:o,character:s}}function r(n,s){let o=0;for(let c=0;c<n;c++)o+=e[c].length;return o+=s,o}return{lines:e,indexToPos:t,posToIndex:r}}class O extends Error{constructor(e){super(e),this.name="ShikiError"}}const $t=new WeakMap;function Te(i,e){$t.set(i,e)}function Z(i){return $t.get(i)}class F{constructor(...e){_(this,"_stacks",{});_(this,"lang");if(e.length===2){const[t,r]=e;this.lang=r,this._stacks=t}else{const[t,r,n]=e;this.lang=r,this._stacks={[n]:t}}}get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(e,t){return new F(Object.fromEntries(hn(t).map(r=>[r,xe])),e)}getInternalStack(e=this.theme){return this._stacks[e]}get scopes(){return at(this._stacks[this.theme])}getScopes(e=this.theme){return at(this._stacks[e])}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.scopes}}}function at(i){const e=[],t=new Set;function r(n){var o;if(t.has(n))return;t.add(n);const s=(o=n==null?void 0:n.nameScopesList)==null?void 0:o.scopeName;s&&e.push(s),n.parent&&r(n.parent)}return r(i),e}function gn(i,e){if(!(i instanceof F))throw new O("Invalid grammar state");return i.getInternalStack(e)}function En(){const i=new WeakMap;function e(t){if(!i.has(t.meta)){let r=function(o){if(typeof o=="number"){if(o<0||o>t.source.length)throw new O(`Invalid decoration offset: ${o}. Code length: ${t.source.length}`);return{...n.indexToPos(o),offset:o}}else{const c=n.lines[o.line];if(c===void 0)throw new O(`Invalid decoration position ${JSON.stringify(o)}. Lines length: ${n.lines.length}`);if(o.character<0||o.character>c.length)throw new O(`Invalid decoration position ${JSON.stringify(o)}. Line ${o.line} length: ${c.length}`);return{...o,offset:n.posToIndex(o.line,o.character)}}};const n=fn(t.source),s=(t.options.decorations||[]).map(o=>({...o,start:r(o.start),end:r(o.end)}));yn(s),i.set(t.meta,{decorations:s,converter:n,source:t.source})}return i.get(t.meta)}return{name:"shiki:decorations",tokens(t){var o;if(!((o=this.options.decorations)!=null&&o.length))return;const n=e(this).decorations.flatMap(c=>[c.start.offset,c.end.offset]);return pn(t,n)},code(t){var m;if(!((m=this.options.decorations)!=null&&m.length))return;const r=e(this),n=Array.from(t.children).filter(h=>h.type==="element"&&h.tagName==="span");if(n.length!==r.converter.lines.length)throw new O(`Number of lines in code element (${n.length}) does not match the number of lines in the source (${r.converter.lines.length}). Failed to apply decorations.`);function s(h,p,d,f){const T=n[h];let E="",g=-1,y=-1;if(p===0&&(g=0),d===0&&(y=0),d===Number.POSITIVE_INFINITY&&(y=T.children.length),g===-1||y===-1)for(let v=0;v<T.children.length;v++)E+=Wt(T.children[v]),g===-1&&E.length===p&&(g=v+1),y===-1&&E.length===d&&(y=v+1);if(g===-1)throw new O(`Failed to find start index for decoration ${JSON.stringify(f.start)}`);if(y===-1)throw new O(`Failed to find end index for decoration ${JSON.stringify(f.end)}`);const R=T.children.slice(g,y);if(!f.alwaysWrap&&R.length===T.children.length)c(T,f,"line");else if(!f.alwaysWrap&&R.length===1&&R[0].type==="element")c(R[0],f,"token");else{const v={type:"element",tagName:"span",properties:{},children:R};c(v,f,"wrapper"),T.children.splice(g,R.length,v)}}function o(h,p){n[h]=c(n[h],p,"line")}function c(h,p,d){var E;const f=p.properties||{},T=p.transform||(g=>g);return h.tagName=p.tagName||"span",h.properties={...h.properties,...f,class:h.properties.class},(E=p.properties)!=null&&E.class&&Mt(h,p.properties.class),h=T(h,d)||h,h}const a=[],u=r.decorations.sort((h,p)=>p.start.offset-h.start.offset);for(const h of u){const{start:p,end:d}=h;if(p.line===d.line)s(p.line,p.character,d.character,h);else if(p.line<d.line){s(p.line,p.character,Number.POSITIVE_INFINITY,h);for(let f=p.line+1;f<d.line;f++)a.unshift(()=>o(f,h));s(d.line,0,d.character,h)}}a.forEach(h=>h())}}}function yn(i){for(let e=0;e<i.length;e++){const t=i[e];if(t.start.offset>t.end.offset)throw new O(`Invalid decoration range: ${JSON.stringify(t.start)} - ${JSON.stringify(t.end)}`);for(let r=e+1;r<i.length;r++){const n=i[r],s=t.start.offset<n.start.offset&&n.start.offset<t.end.offset,o=t.start.offset<n.end.offset&&n.end.offset<t.end.offset,c=n.start.offset<t.start.offset&&t.start.offset<n.end.offset,a=n.start.offset<t.end.offset&&t.end.offset<n.end.offset;if(s||o||c||a){if(o&&o||c&&a)continue;throw new O(`Decorations ${JSON.stringify(t.start)} and ${JSON.stringify(n.start)} intersect.`)}}}}function Wt(i){return i.type==="text"?i.value:i.type==="element"?i.children.map(Wt).join(""):""}const Rn=[En()];function _e(i){return[...i.transformers||[],...Rn]}var $=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],we={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",9:"strikethrough"};function Tn(i,e){const t=i.indexOf("\x1B[",e);if(t!==-1){const r=i.indexOf("m",t);return{sequence:i.substring(t+2,r).split(";"),startPosition:t,position:r+1}}return{position:i.length}}function ct(i,e){let t=1;const r=i[e+t++];let n;if(r==="2"){const s=[i[e+t++],i[e+t++],i[e+t]].map(o=>Number.parseInt(o));s.length===3&&!s.some(o=>Number.isNaN(o))&&(n={type:"rgb",rgb:s})}else if(r==="5"){const s=Number.parseInt(i[e+t]);Number.isNaN(s)||(n={type:"table",index:Number(s)})}return[t,n]}function vn(i){const e=[];for(let t=0;t<i.length;t++){const r=i[t],n=Number.parseInt(r);if(!Number.isNaN(n))if(n===0)e.push({type:"resetAll"});else if(n<=9)we[n]&&e.push({type:"setDecoration",value:we[n]});else if(n<=29){const s=we[n-20];s&&e.push({type:"resetDecoration",value:s})}else if(n<=37)e.push({type:"setForegroundColor",value:{type:"named",name:$[n-30]}});else if(n===38){const[s,o]=ct(i,t);o&&e.push({type:"setForegroundColor",value:o}),t+=s}else if(n===39)e.push({type:"resetForegroundColor"});else if(n<=47)e.push({type:"setBackgroundColor",value:{type:"named",name:$[n-40]}});else if(n===48){const[s,o]=ct(i,t);o&&e.push({type:"setBackgroundColor",value:o}),t+=s}else n===49?e.push({type:"resetBackgroundColor"}):n>=90&&n<=97?e.push({type:"setForegroundColor",value:{type:"named",name:$[n-90+8]}}):n>=100&&n<=107&&e.push({type:"setBackgroundColor",value:{type:"named",name:$[n-100+8]}})}return e}function An(){let i=null,e=null,t=new Set;return{parse(r){const n=[];let s=0;do{const o=Tn(r,s),c=o.sequence?r.substring(s,o.startPosition):r.substring(s);if(c.length>0&&n.push({value:c,foreground:i,background:e,decorations:new Set(t)}),o.sequence){const a=vn(o.sequence);for(const u of a)u.type==="resetAll"?(i=null,e=null,t.clear()):u.type==="resetForegroundColor"?i=null:u.type==="resetBackgroundColor"?e=null:u.type==="resetDecoration"&&t.delete(u.value);for(const u of a)u.type==="setForegroundColor"?i=u.value:u.type==="setBackgroundColor"?e=u.value:u.type==="setDecoration"&&t.add(u.value)}s=o.position}while(s<r.length);return n}}}var Ln={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function Pn(i=Ln){function e(c){return i[c]}function t(c){return`#${c.map(a=>Math.max(0,Math.min(a,255)).toString(16).padStart(2,"0")).join("")}`}let r;function n(){if(r)return r;r=[];for(let u=0;u<$.length;u++)r.push(e($[u]));let c=[0,95,135,175,215,255];for(let u=0;u<6;u++)for(let m=0;m<6;m++)for(let h=0;h<6;h++)r.push(t([c[u],c[m],c[h]]));let a=8;for(let u=0;u<24;u++,a+=10)r.push(t([a,a,a]));return r}function s(c){return n()[c]}function o(c){switch(c.type){case"named":return e(c.name);case"rgb":return t(c.rgb);case"table":return s(c.index)}}return{value:o}}function bn(i,e,t){const r=pe(i,t),n=Re(e),s=Pn(Object.fromEntries($.map(c=>{var a;return[c,(a=i.colors)==null?void 0:a[`terminal.ansi${c[0].toUpperCase()}${c.substring(1)}`]]}))),o=An();return n.map(c=>o.parse(c[0]).map(a=>{let u,m;a.decorations.has("reverse")?(u=a.background?s.value(a.background):i.bg,m=a.foreground?s.value(a.foreground):i.fg):(u=a.foreground?s.value(a.foreground):i.fg,m=a.background?s.value(a.background):void 0),u=j(u,r),m=j(m,r),a.decorations.has("dim")&&(u=In(u));let h=M.None;return a.decorations.has("bold")&&(h|=M.Bold),a.decorations.has("italic")&&(h|=M.Italic),a.decorations.has("underline")&&(h|=M.Underline),{content:a.value,offset:c[1],color:u,bgColor:m,fontStyle:h}}))}function In(i){const e=i.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(e)if(e[3]){const r=Math.round(Number.parseInt(e[3],16)/2).toString(16).padStart(2,"0");return`#${e[1]}${e[2]}${r}`}else return e[2]?`#${e[1]}${e[2]}80`:`#${Array.from(e[1]).map(r=>`${r}${r}`).join("")}80`;const t=i.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return t?`var(${t[1]}-dim)`:i}function He(i,e,t={}){const{lang:r="text",theme:n=i.getLoadedThemes()[0]}=t;if(We(r)||Ue(n))return Re(e).map(a=>[{content:a[0],offset:a[1]}]);const{theme:s,colorMap:o}=i.setTheme(n);if(r==="ansi")return bn(s,e,t);const c=i.getLanguage(r);if(t.grammarState){if(t.grammarState.lang!==c.name)throw new B(`Grammar state language "${t.grammarState.lang}" does not match highlight language "${c.name}"`);if(!t.grammarState.themes.includes(s.name))throw new B(`Grammar state themes "${t.grammarState.themes}" do not contain highlight theme "${s.name}"`)}return On(e,c,s,o,t)}function Sn(...i){if(i.length===2)return Z(i[1]);const[e,t,r={}]=i,{lang:n="text",theme:s=e.getLoadedThemes()[0]}=r;if(We(n)||Ue(s))throw new B("Plain language does not have grammar state");if(n==="ansi")throw new B("ANSI language does not have grammar state");const{theme:o,colorMap:c}=e.setTheme(s),a=e.getLanguage(n);return new F(fe(t,a,o,c,r).stateStack,a.name,o.name)}function On(i,e,t,r,n){const s=fe(i,e,t,r,n),o=new F(fe(i,e,t,r,n).stateStack,e.name,t.name);return Te(s.tokens,o),s.tokens}function fe(i,e,t,r,n){const s=pe(t,n),{tokenizeMaxLineLength:o=0,tokenizeTimeLimit:c=500}=n,a=Re(i);let u=n.grammarState?gn(n.grammarState,t.name)??xe:n.grammarContextCode!=null?fe(n.grammarContextCode,e,t,r,{...n,grammarState:void 0,grammarContextCode:void 0}).stateStack:xe,m=[];const h=[];for(let p=0,d=a.length;p<d;p++){const[f,T]=a[p];if(f===""){m=[],h.push([]);continue}if(o>0&&f.length>=o){m=[],h.push([{content:f,offset:T,color:"",fontStyle:0}]);continue}let E,g,y;n.includeExplanation&&(E=e.tokenizeLine(f,u),g=E.tokens,y=0);const R=e.tokenizeLine2(f,u,c),v=R.tokens.length/2;for(let A=0;A<v;A++){const w=R.tokens[2*A],L=A+1<v?R.tokens[2*A+2]:f.length;if(w===L)continue;const V=R.tokens[2*A+1],re=j(r[H.getForeground(V)],s),q=H.getFontStyle(V),ve={content:f.substring(w,L),offset:T+w,color:re,fontStyle:q};if(n.includeExplanation){const qe=[];if(n.includeExplanation!=="scopeName")for(const x of t.settings){let W;switch(typeof x.scope){case"string":W=x.scope.split(/,/).map(Ae=>Ae.trim());break;case"object":W=x.scope;break;default:continue}qe.push({settings:x,selectors:W.map(Ae=>Ae.split(/ /))})}ve.explanation=[];let ze=0;for(;w+ze<L;){const x=g[y],W=f.substring(x.startIndex,x.endIndex);ze+=W.length,ve.explanation.push({content:W,scopes:n.includeExplanation==="scopeName"?wn(x.scopes):Cn(qe,x.scopes)}),y+=1}}m.push(ve)}h.push(m),m=[],u=R.ruleStack}return{tokens:h,stateStack:u}}function wn(i){return i.map(e=>({scopeName:e}))}function Cn(i,e){const t=[];for(let r=0,n=e.length;r<n;r++){const s=e[r];t[r]={scopeName:s,themeMatches:Dn(i,s,e.slice(0,r))}}return t}function lt(i,e){return i===e||e.substring(0,i.length)===i&&e[i.length]==="."}function kn(i,e,t){if(!lt(i[i.length-1],e))return!1;let r=i.length-2,n=t.length-1;for(;r>=0&&n>=0;)lt(i[r],t[n])&&(r-=1),n-=1;return r===-1}function Dn(i,e,t){const r=[];for(const{selectors:n,settings:s}of i)for(const o of n)if(kn(o,e,t)){r.push(s);break}return r}function Ut(i,e,t){const r=Object.entries(t.themes).filter(a=>a[1]).map(a=>({color:a[0],theme:a[1]})),n=r.map(a=>{const u=He(i,e,{...t,theme:a.theme}),m=Z(u),h=typeof a.theme=="string"?a.theme:a.theme.name;return{tokens:u,state:m,theme:h}}),s=Nn(...n.map(a=>a.tokens)),o=s[0].map((a,u)=>a.map((m,h)=>{const p={content:m.content,variants:{},offset:m.offset};return"includeExplanation"in t&&t.includeExplanation&&(p.explanation=m.explanation),s.forEach((d,f)=>{const{content:T,explanation:E,offset:g,...y}=d[u][h];p.variants[r[f].color]=y}),p})),c=n[0].state?new F(Object.fromEntries(n.map(a=>{var u;return[a.theme,(u=a.state)==null?void 0:u.getInternalStack(a.theme)]})),n[0].state.lang):void 0;return c&&Te(o,c),o}function Nn(...i){const e=i.map(()=>[]),t=i.length;for(let r=0;r<i[0].length;r++){const n=i.map(a=>a[r]),s=e.map(()=>[]);e.forEach((a,u)=>a.push(s[u]));const o=n.map(()=>0),c=n.map(a=>a[0]);for(;c.every(a=>a);){const a=Math.min(...c.map(u=>u.content.length));for(let u=0;u<t;u++){const m=c[u];m.content.length===a?(s[u].push(m),o[u]+=1,c[u]=n[u][o[u]]):(s[u].push({...m,content:m.content.slice(0,a)}),c[u]={...m,content:m.content.slice(a),offset:m.offset+a})}}}return e}function ge(i,e,t){let r,n,s,o,c,a;if("themes"in t){const{defaultColor:u="light",cssVariablePrefix:m="--shiki-"}=t,h=Object.entries(t.themes).filter(E=>E[1]).map(E=>({color:E[0],theme:E[1]})).sort((E,g)=>E.color===u?-1:g.color===u?1:0);if(h.length===0)throw new B("`themes` option must not be empty");const p=Ut(i,e,t);if(a=Z(p),u&&!h.find(E=>E.color===u))throw new B(`\`themes\` option must contain the defaultColor key \`${u}\``);const d=h.map(E=>i.getTheme(E.theme)),f=h.map(E=>E.color);s=p.map(E=>E.map(g=>Vn(g,f,m,u))),a&&Te(s,a);const T=h.map(E=>pe(E.theme,t));n=h.map((E,g)=>(g===0&&u?"":`${m+E.color}:`)+(j(d[g].fg,T[g])||"inherit")).join(";"),r=h.map((E,g)=>(g===0&&u?"":`${m+E.color}-bg:`)+(j(d[g].bg,T[g])||"inherit")).join(";"),o=`shiki-themes ${d.map(E=>E.name).join(" ")}`,c=u?void 0:[n,r].join(";")}else if("theme"in t){const u=pe(t.theme,t);s=He(i,e,t);const m=i.getTheme(t.theme);r=j(m.bg,u),n=j(m.fg,u),o=m.name,a=Z(s)}else throw new B("Invalid options, either `theme` or `themes` must be provided");return{tokens:s,fg:n,bg:r,themeName:o,rootStyle:c,grammarState:a}}function Vn(i,e,t,r){const n={content:i.content,explanation:i.explanation,offset:i.offset},s=e.map(a=>jt(i.variants[a])),o=new Set(s.flatMap(a=>Object.keys(a))),c={};return s.forEach((a,u)=>{for(const m of o){const h=a[m]||"inherit";if(u===0&&r)c[m]=h;else{const p=m==="color"?"":m==="background-color"?"-bg":`-${m}`,d=t+e[u]+(m==="color"?"":p);c[d]=h}}}),n.htmlStyle=c,n}function Ee(i,e,t,r={meta:{},options:t,codeToHast:(n,s)=>Ee(i,n,s),codeToTokens:(n,s)=>ge(i,n,s)}){var d,f;let n=e;for(const T of _e(t))n=((d=T.preprocess)==null?void 0:d.call(r,n,t))||n;let{tokens:s,fg:o,bg:c,themeName:a,rootStyle:u,grammarState:m}=ge(i,n,t);const{mergeWhitespaces:h=!0}=t;h===!0?s=Gn(s):h==="never"&&(s=Mn(s));const p={...r,get source(){return n}};for(const T of _e(t))s=((f=T.tokens)==null?void 0:f.call(p,s))||s;return xn(s,{...t,fg:o,bg:c,themeName:a,rootStyle:u},p,m)}function xn(i,e,t,r=Z(i)){var f,T,E;const n=_e(e),s=[],o={type:"root",children:[]},{structure:c="classic",tabindex:a="0"}=e;let u={type:"element",tagName:"pre",properties:{class:`shiki ${e.themeName||""}`,style:e.rootStyle||`background-color:${e.bg};color:${e.fg}`,...a!==!1&&a!=null?{tabindex:a.toString()}:{},...Object.fromEntries(Array.from(Object.entries(e.meta||{})).filter(([g])=>!g.startsWith("_")))},children:[]},m={type:"element",tagName:"code",properties:{},children:s};const h=[],p={...t,structure:c,addClassToHast:Mt,get source(){return t.source},get tokens(){return i},get options(){return e},get root(){return o},get pre(){return u},get code(){return m},get lines(){return h}};if(i.forEach((g,y)=>{var A,w;y&&(c==="inline"?o.children.push({type:"element",tagName:"br",properties:{},children:[]}):c==="classic"&&s.push({type:"text",value:`
`}));let R={type:"element",tagName:"span",properties:{class:"line"},children:[]},v=0;for(const L of g){let V={type:"element",tagName:"span",properties:{...L.htmlAttrs},children:[{type:"text",value:L.content}]};L.htmlStyle;const re=_n(L.htmlStyle||jt(L));re&&(V.properties.style=re);for(const q of n)V=((A=q==null?void 0:q.span)==null?void 0:A.call(p,V,y+1,v,R,L))||V;c==="inline"?o.children.push(V):c==="classic"&&R.children.push(V),v+=L.content.length}if(c==="classic"){for(const L of n)R=((w=L==null?void 0:L.line)==null?void 0:w.call(p,R,y+1))||R;h.push(R),s.push(R)}}),c==="classic"){for(const g of n)m=((f=g==null?void 0:g.code)==null?void 0:f.call(p,m))||m;u.children.push(m);for(const g of n)u=((T=g==null?void 0:g.pre)==null?void 0:T.call(p,u))||u;o.children.push(u)}let d=o;for(const g of n)d=((E=g==null?void 0:g.root)==null?void 0:E.call(p,d))||d;return r&&Te(d,r),d}function Gn(i){return i.map(e=>{const t=[];let r="",n=0;return e.forEach((s,o)=>{const a=!(s.fontStyle&&s.fontStyle&M.Underline);a&&s.content.match(/^\s+$/)&&e[o+1]?(n||(n=s.offset),r+=s.content):r?(a?t.push({...s,offset:n,content:r+s.content}):t.push({content:r,offset:n},s),n=0,r=""):t.push(s)}),t})}function Mn(i){return i.map(e=>e.flatMap(t=>{if(t.content.match(/^\s+$/))return t;const r=t.content.match(/^(\s*)(.*?)(\s*)$/);if(!r)return t;const[,n,s,o]=r;if(!n&&!o)return t;const c=[{...t,offset:t.offset+n.length,content:s}];return n&&c.unshift({content:n,offset:t.offset}),o&&c.push({content:o,offset:t.offset+n.length+s.length}),c}))}function Bn(i,e,t){var s;const r={meta:{},options:t,codeToHast:(o,c)=>Ee(i,o,c),codeToTokens:(o,c)=>ge(i,o,c)};let n=ln(Ee(i,e,t,r));for(const o of _e(t))n=((s=o.postprocess)==null?void 0:s.call(r,n,t))||n;return n}const ut={light:"#333333",dark:"#bbbbbb"},mt={light:"#fffffe",dark:"#1e1e1e"},ht="__shiki_resolved";function Fe(i){var c,a,u,m,h;if(i!=null&&i[ht])return i;const e={...i};e.tokenColors&&!e.settings&&(e.settings=e.tokenColors,delete e.tokenColors),e.type||(e.type="dark"),e.colorReplacements={...e.colorReplacements},e.settings||(e.settings=[]);let{bg:t,fg:r}=e;if(!t||!r){const p=e.settings?e.settings.find(d=>!d.name&&!d.scope):void 0;(c=p==null?void 0:p.settings)!=null&&c.foreground&&(r=p.settings.foreground),(a=p==null?void 0:p.settings)!=null&&a.background&&(t=p.settings.background),!r&&((u=e==null?void 0:e.colors)!=null&&u["editor.foreground"])&&(r=e.colors["editor.foreground"]),!t&&((m=e==null?void 0:e.colors)!=null&&m["editor.background"])&&(t=e.colors["editor.background"]),r||(r=e.type==="light"?ut.light:ut.dark),t||(t=e.type==="light"?mt.light:mt.dark),e.fg=r,e.bg=t}e.settings[0]&&e.settings[0].settings&&!e.settings[0].scope||e.settings.unshift({settings:{foreground:e.fg,background:e.bg}});let n=0;const s=new Map;function o(p){var f;if(s.has(p))return s.get(p);n+=1;const d=`#${n.toString(16).padStart(8,"0").toLowerCase()}`;return(f=e.colorReplacements)!=null&&f[`#${d}`]?o(p):(s.set(p,d),d)}e.settings=e.settings.map(p=>{var E,g;const d=((E=p.settings)==null?void 0:E.foreground)&&!p.settings.foreground.startsWith("#"),f=((g=p.settings)==null?void 0:g.background)&&!p.settings.background.startsWith("#");if(!d&&!f)return p;const T={...p,settings:{...p.settings}};if(d){const y=o(p.settings.foreground);e.colorReplacements[y]=p.settings.foreground,T.settings.foreground=y}if(f){const y=o(p.settings.background);e.colorReplacements[y]=p.settings.background,T.settings.background=y}return T});for(const p of Object.keys(e.colors||{}))if((p==="editor.foreground"||p==="editor.background"||p.startsWith("terminal.ansi"))&&!((h=e.colors[p])!=null&&h.startsWith("#"))){const d=o(e.colors[p]);e.colorReplacements[d]=e.colors[p],e.colors[p]=d}return Object.defineProperty(e,ht,{enumerable:!1,writable:!1,value:!0}),e}async function Ht(i){return Array.from(new Set((await Promise.all(i.filter(e=>!xt(e)).map(async e=>await Bt(e).then(t=>Array.isArray(t)?t:[t])))).flat()))}async function Ft(i){return(await Promise.all(i.map(async t=>Gt(t)?null:Fe(await Bt(t))))).filter(t=>!!t)}class jn extends ai{constructor(t,r,n,s={}){super(t);_(this,"_resolvedThemes",new Map);_(this,"_resolvedGrammars",new Map);_(this,"_langMap",new Map);_(this,"_langGraph",new Map);_(this,"_textmateThemeCache",new WeakMap);_(this,"_loadedThemesCache",null);_(this,"_loadedLanguagesCache",null);this._resolver=t,this._themes=r,this._langs=n,this._alias=s,this._themes.map(o=>this.loadTheme(o)),this.loadLanguages(this._langs)}getTheme(t){return typeof t=="string"?this._resolvedThemes.get(t):this.loadTheme(t)}loadTheme(t){const r=Fe(t);return r.name&&(this._resolvedThemes.set(r.name,r),this._loadedThemesCache=null),r}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(t){let r=this._textmateThemeCache.get(t);r||(r=le.createFromRawTheme(t),this._textmateThemeCache.set(t,r)),this._syncRegistry.setTheme(r)}getGrammar(t){if(this._alias[t]){const r=new Set([t]);for(;this._alias[t];){if(t=this._alias[t],r.has(t))throw new O(`Circular alias \`${Array.from(r).join(" -> ")} -> ${t}\``);r.add(t)}}return this._resolvedGrammars.get(t)}loadLanguage(t){var o,c,a,u;if(this.getGrammar(t.name))return;const r=new Set([...this._langMap.values()].filter(m=>{var h;return(h=m.embeddedLangsLazy)==null?void 0:h.includes(t.name)}));this._resolver.addLanguage(t);const n={balancedBracketSelectors:t.balancedBracketSelectors||["*"],unbalancedBracketSelectors:t.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(t.scopeName,t);const s=this.loadGrammarWithConfiguration(t.scopeName,1,n);if(s.name=t.name,this._resolvedGrammars.set(t.name,s),t.aliases&&t.aliases.forEach(m=>{this._alias[m]=t.name}),this._loadedLanguagesCache=null,r.size)for(const m of r)this._resolvedGrammars.delete(m.name),this._loadedLanguagesCache=null,(c=(o=this._syncRegistry)==null?void 0:o._injectionGrammars)==null||c.delete(m.scopeName),(u=(a=this._syncRegistry)==null?void 0:a._grammars)==null||u.delete(m.scopeName),this.loadLanguage(this._langMap.get(m.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(t){for(const s of t)this.resolveEmbeddedLanguages(s);const r=Array.from(this._langGraph.entries()),n=r.filter(([s,o])=>!o);if(n.length){const s=r.filter(([o,c])=>{var a;return c&&((a=c.embeddedLangs)==null?void 0:a.some(u=>n.map(([m])=>m).includes(u)))}).filter(o=>!n.includes(o));throw new O(`Missing languages ${n.map(([o])=>`\`${o}\``).join(", ")}, required by ${s.map(([o])=>`\`${o}\``).join(", ")}`)}for(const[s,o]of r)this._resolver.addLanguage(o);for(const[s,o]of r)this.loadLanguage(o)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(t){if(this._langMap.set(t.name,t),this._langGraph.set(t.name,t),t.embeddedLangs)for(const r of t.embeddedLangs)this._langGraph.set(r,this._langMap.get(r))}}class $n{constructor(e,t){_(this,"_langs",new Map);_(this,"_scopeToLang",new Map);_(this,"_injections",new Map);_(this,"_onigLib");this._onigLib={createOnigScanner:r=>e.createScanner(r),createOnigString:r=>e.createString(r)},t.forEach(r=>this.addLanguage(r))}get onigLib(){return this._onigLib}getLangRegistration(e){return this._langs.get(e)}loadGrammar(e){return this._scopeToLang.get(e)}addLanguage(e){this._langs.set(e.name,e),e.aliases&&e.aliases.forEach(t=>{this._langs.set(t,e)}),this._scopeToLang.set(e.scopeName,e),e.injectTo&&e.injectTo.forEach(t=>{this._injections.get(t)||this._injections.set(t,[]),this._injections.get(t).push(e.scopeName)})}getInjections(e){const t=e.split(".");let r=[];for(let n=1;n<=t.length;n++){const s=t.slice(0,n).join(".");r=[...r,...this._injections.get(s)||[]]}return r}}let z=0;function Wn(i){z+=1,i.warnings!==!1&&z>=10&&z%10===0&&console.warn(`[Shiki] ${z} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let e=!1;if(!i.engine)throw new O("`engine` option is required for synchronous mode");const t=(i.langs||[]).flat(1),r=(i.themes||[]).flat(1).map(Fe),n=new $n(i.engine,t),s=new jn(n,r,t,i.langAlias);let o;function c(y){E();const R=s.getGrammar(typeof y=="string"?y:y.name);if(!R)throw new O(`Language \`${y}\` not found, you may need to load it first`);return R}function a(y){if(y==="none")return{bg:"",fg:"",name:"none",settings:[],type:"dark"};E();const R=s.getTheme(y);if(!R)throw new O(`Theme \`${y}\` not found, you may need to load it first`);return R}function u(y){E();const R=a(y);o!==y&&(s.setTheme(R),o=y);const v=s.getColorMap();return{theme:R,colorMap:v}}function m(){return E(),s.getLoadedThemes()}function h(){return E(),s.getLoadedLanguages()}function p(...y){E(),s.loadLanguages(y.flat(1))}async function d(...y){return p(await Ht(y))}function f(...y){E();for(const R of y.flat(1))s.loadTheme(R)}async function T(...y){return E(),f(await Ft(y))}function E(){if(e)throw new O("Shiki instance has been disposed")}function g(){e||(e=!0,s.dispose(),z-=1)}return{setTheme:u,getTheme:a,getLanguage:c,getLoadedThemes:m,getLoadedLanguages:h,loadLanguage:d,loadLanguageSync:p,loadTheme:T,loadThemeSync:f,dispose:g,[Symbol.dispose]:g}}async function Un(i={}){i.loadWasm;const[e,t,r]=await Promise.all([Ft(i.themes||[]),Ht(i.langs||[]),i.engine||_t(i.loadWasm||Tr())]);return Wn({...i,themes:e,langs:t,engine:r})}async function Hn(i={}){const e=await Un(i);return{getLastGrammarState:(...t)=>Sn(e,...t),codeToTokensBase:(t,r)=>He(e,t,r),codeToTokensWithThemes:(t,r)=>Ut(e,t,r),codeToTokens:(t,r)=>ge(e,t,r),codeToHast:(t,r)=>Ee(e,t,r),codeToHtml:(t,r)=>Bn(e,t,r),...e,getInternalContext:()=>e}}function Fn(i,e,t){let r,n,s;{const c=i;r=c.langs,n=c.themes,s=c.engine}async function o(c){function a(d){if(typeof d=="string"){if(xt(d))return[];const f=r[d];if(!f)throw new B(`Language \`${d}\` is not included in this bundle. You may want to load it from external source.`);return f}return d}function u(d){if(Gt(d))return"none";if(typeof d=="string"){const f=n[d];if(!f)throw new B(`Theme \`${d}\` is not included in this bundle. You may want to load it from external source.`);return f}return d}const m=(c.themes??[]).map(d=>u(d)),h=(c.langs??[]).map(d=>a(d)),p=await Hn({engine:c.engine??s(),...c,themes:m,langs:h});return{...p,loadLanguage(...d){return p.loadLanguage(...d.map(a))},loadTheme(...d){return p.loadTheme(...d.map(u))}}}return o}function qn(i){let e;async function t(r={}){if(e){const n=await e;return await Promise.all([n.loadTheme(...r.themes||[]),n.loadLanguage(...r.langs||[])]),n}else return e=i({...r,themes:r.themes||[],langs:r.langs||[]}),e}return t}function zn(i){const e=qn(i);return{getSingletonHighlighter(t){return e(t)},async codeToHtml(t,r){return(await e({langs:[r.lang],themes:"theme"in r?[r.theme]:Object.values(r.themes)})).codeToHtml(t,r)},async codeToHast(t,r){return(await e({langs:[r.lang],themes:"theme"in r?[r.theme]:Object.values(r.themes)})).codeToHast(t,r)},async codeToTokens(t,r){return(await e({langs:[r.lang],themes:"theme"in r?[r.theme]:Object.values(r.themes)})).codeToTokens(t,r)},async codeToTokensBase(t,r){return(await e({langs:[r.lang],themes:[r.theme]})).codeToTokensBase(t,r)},async codeToTokensWithThemes(t,r){return(await e({langs:[r.lang],themes:Object.values(r.themes).filter(Boolean)})).codeToTokensWithThemes(t,r)},async getLastGrammarState(t,r){return(await e({langs:[r.lang],themes:[r.theme]})).getLastGrammarState(t,r)}}}const Jn=Fn({langs:tr,themes:ir,engine:()=>_t(l(()=>import("./wasm-CG6Dc4jp.js"),[]))}),{codeToTokens:es}=zn(Jn);export{M as FontStyle,B as ShikiError,H as StackElementMetadata,Mt as addClassToHast,j as applyColorReplacements,tr as bundledLanguages,er as bundledLanguagesAlias,Zt as bundledLanguagesBase,pt as bundledLanguagesInfo,ir as bundledThemes,rr as bundledThemesInfo,es as codeToTokens,Jn as createHighlighter,Hn as createHighlighterCore,_t as createOnigurumaEngine,fn as createPositionConverter,Un as createShikiInternal,Wn as createShikiInternalSync,zn as createSingletonShorthands,Fn as createdBundledHighlighter,jt as getTokenStyleObject,ln as hastToHtml,Ue as isNoneTheme,We as isPlainLang,xt as isSpecialLang,Gt as isSpecialTheme,gr as loadWasm,qn as makeSingletonHighlighter,Bt as normalizeGetter,Fe as normalizeTheme,pe as resolveColorReplacements,Re as splitLines,dn as splitToken,pn as splitTokens,_n as stringifyTokenStyle,hn as toArray,bn as tokenizeAnsiWithTheme,On as tokenizeWithTheme,xn as tokensToHast,En as transformerDecorations};
