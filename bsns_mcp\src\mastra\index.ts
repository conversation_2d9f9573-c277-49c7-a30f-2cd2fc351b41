
import 'dotenv/config';
import { <PERSON>stra } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { createBusinessAssistant } from './agents/business-assistant';
import { createBsnsAgent } from './agents/bsns-agent';

export const mastra = new Mastra({
  llms: {
    openai: openai({
      apiKey: process.env.OPENAI_API_KEY,
    }),
  },
});

export const businessAssistant = createBusinessAssistant(mastra);
export const bsnsAgent = createBsnsAgent(mastra);