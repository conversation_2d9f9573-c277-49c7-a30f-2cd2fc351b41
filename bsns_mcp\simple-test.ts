import 'dotenv/config';
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';

async function testOpenAI() {
  try {
    console.log('🚀 Testing OpenAI integration...\n');

    // Debug: Check if API key is loaded
    const apiKey = process.env.OPENAI_API_KEY;
    console.log('API Key loaded:', apiKey ? `${apiKey.substring(0, 10)}...` : 'NOT FOUND');

    const result = await generateText({
      model: openai('gpt-4o-mini'),
      prompt: 'What are 3 key factors to consider when starting a new business?',
    });

    console.log('✅ OpenAI Response:');
    console.log(result.text);

  } catch (error) {
    console.error('❌ Error:', error);
    console.error('Error details:', error.message);
  }
}

testOpenAI();
