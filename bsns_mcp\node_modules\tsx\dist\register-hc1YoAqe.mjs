var d=Object.defineProperty;var o=(r,s)=>d(r,"name",{value:s,configurable:!0});import m from"node:module";import{MessageChannel as u}from"node:worker_threads";import{f as g,a as v}from"./register-CuoYSLaL.mjs";import{pathToFileURL as h}from"node:url";const w=o(r=>(s,e)=>{if(!e)throw new Error("The current file path (import.meta.url) must be provided in the second argument of tsImport()");const a=e.startsWith(g)?e:h(e).toString();return import(`tsx://${JSON.stringify({specifier:s,parentURL:a,namespace:r})}`)},"createScopedImport");let l=!1;const E=o(r=>{if(!m.register)throw new Error(`This version of Node.js (${process.version}) does not support module.register(). Please upgrade to Node v18.19 or v20.6 and above.`);if(!l){const{_resolveFilename:t}=m;m._resolveFilename=(p,...c)=>t(v(p),...c),l=!0}const{sourceMapsEnabled:s}=process;process.setSourceMapsEnabled(!0);const{port1:e,port2:a}=new u;m.register(`./esm/index.mjs?${Date.now()}`,{parentURL:import.meta.url,data:{port:a,namespace:r?.namespace,tsconfig:r?.tsconfig},transferList:[a]});const f=r?.onImport,n=f&&(t=>{t.type==="load"&&f(t.url)});n&&(e.on("message",n),e.unref());const i=o(()=>(s===!1&&process.setSourceMapsEnabled(!1),n&&e.off("message",n),e.postMessage("deactivate"),new Promise(t=>{const p=o(c=>{c.type==="deactivated"&&(t(),e.off("message",p))},"onDeactivated");e.on("message",p)})),"unregister");return r?.namespace&&(i.import=w(r.namespace),i.unregister=i),i},"register");export{E as r};
