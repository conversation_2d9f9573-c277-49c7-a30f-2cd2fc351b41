import 'dotenv/config';
import { mastra, businessAssistant } from './src/mastra/index';

async function testBusinessAssistant() {
  try {
    console.log('🚀 Testing Business Assistant...\n');

    // Test basic conversation
    const response = await businessAssistant.generate(
      'What are the key factors to consider when starting a new business? Please provide 3 main points.'
    );

    console.log('💼 Business Assistant Response:');
    console.log(response.text);

  } catch (error) {
    console.error('❌ Error:', error);
    console.error('Error details:', error.message);
  }
}

// Run the test
testBusinessAssistant();
