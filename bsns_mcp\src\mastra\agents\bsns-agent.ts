import { Agent } from '@mastra/core/agent';
import { z } from 'zod';
import axios from 'axios';

// URL Özet Tool - bsns-mcp API'si ile entegre
const summarizeUrl = {
  name: 'summarize_url',
  description: 'Verilen URL\'yi bsns-mcp API\'sine gönderir ve özet alır',
  parameters: z.object({
    url: z.string().url().describe('Özetlenecek web sayfasının URL\'si'),
    language: z.string().optional().default('tr').describe('Özet dili (tr, en, vb.)'),
  }),
  execute: async ({ url, language = 'tr' }: { url: string; language?: string }) => {
    try {
      console.log('🔗 URL özetleniyor:', url);

      // bsns-mcp API'sine istek gönder
      const response = await axios.post('http://localhost:3001/api/summarize', {
        url: url,
        language: language,
        options: {
          maxLength: 500,
          includeKeyPoints: true,
          includeCategories: true
        }
      }, {
        timeout: 30000, // 30 saniye timeout
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'BSNS-MCP-Agent/1.0'
        }
      });

      const summary = response.data;

      console.log('✅ Özet alındı:', summary.title);

      return {
        success: true,
        title: summary.title || 'Başlık bulunamadı',
        summary: summary.summary || 'Özet oluşturulamadı',
        keyPoints: summary.keyPoints || [],
        categories: summary.categories || [],
        wordCount: summary.wordCount || 0,
        readingTime: summary.readingTime || '1 dakika',
        url: url,
        language: language,
        message: `"${summary.title}" başlıklı sayfa başarıyla özetlendi.`
      };

    } catch (error) {
      console.error('❌ URL özet hatası:', error.message);

      // Hata durumunda fallback özet
      if (error.code === 'ECONNREFUSED') {
        return {
          success: false,
          error: 'bsns-mcp API\'sine bağlanılamadı. API\'nin çalıştığından emin olun.',
          url: url,
          message: 'API bağlantı hatası - lütfen daha sonra tekrar deneyin.'
        };
      }

      if (error.response?.status === 404) {
        return {
          success: false,
          error: 'URL bulunamadı veya erişilemiyor.',
          url: url,
          message: 'Belirtilen URL\'ye erişilemedi.'
        };
      }

      return {
        success: false,
        error: error.message || 'Bilinmeyen hata',
        url: url,
        message: 'URL özetlenirken bir hata oluştu.'
      };
    }
  },
};

// URL Doğrulama Tool
const validateUrl = {
  name: 'validate_url',
  description: 'URL\'nin geçerli ve erişilebilir olup olmadığını kontrol eder',
  parameters: z.object({
    url: z.string().describe('Kontrol edilecek URL'),
  }),
  execute: async ({ url }: { url: string }) => {
    try {
      // URL formatını kontrol et
      new URL(url);

      // URL\'ye HEAD request gönder
      const response = await axios.head(url, {
        timeout: 10000,
        maxRedirects: 5
      });

      return {
        valid: true,
        accessible: true,
        statusCode: response.status,
        contentType: response.headers['content-type'] || 'unknown',
        message: 'URL geçerli ve erişilebilir'
      };

    } catch (error) {
      return {
        valid: url.startsWith('http'),
        accessible: false,
        error: error.message,
        message: 'URL geçersiz veya erişilemiyor'
      };
    }
  },
};

// BSNS URL Özet Agent'ı Factory Function
export function createBsnsAgent(mastra: any) {
  return new Agent({
    name: 'BSNS URL Özet Agent',
    instructions: `Sen BSNS URL Özet Agent'ısın. Görevin:

1. 🔗 Kullanıcıdan URL al
2. ✅ URL'nin geçerliliğini kontrol et
3. 📄 URL'yi bsns-mcp API'sine gönder
4. 📝 Gelen özeti kullanıcıya sun

ÖZELLİKLERİN:
- URL doğrulama ve erişilebilirlik kontrolü
- Türkçe ve İngilizce özet desteği
- Anahtar noktalar ve kategoriler
- Okuma süresi tahmini
- Hata yönetimi ve kullanıcı dostu mesajlar

KULLANIM:
- Kullanıcı bir URL verdiğinde önce doğrula
- Sonra bsns-mcp API ile özetle
- Sonuçları düzenli ve anlaşılır şekilde sun
- Hata durumunda açıklayıcı mesajlar ver

Her zaman Türkçe yanıt ver ve profesyonel ol.`,

    model: {
      provider: 'openai',
      name: 'gpt-4o-mini',
    },

    tools: [summarizeUrl, validateUrl],
    mastra,
  });
}
