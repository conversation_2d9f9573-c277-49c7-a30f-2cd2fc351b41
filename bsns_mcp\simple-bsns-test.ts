import 'dotenv/config';
import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import axios from 'axios';

// Basit URL Doğrulama Tool
const validateUrl = tool({
  description: 'URL\'nin geçerli olup olmadığını kontrol eder',
  parameters: z.object({
    url: z.string().describe('Kontrol edilecek URL'),
  }),
  execute: async ({ url }) => {
    try {
      new URL(url);
      return {
        valid: true,
        message: `✅ "${url}" geçerli bir URL formatında`
      };
    } catch {
      return {
        valid: false,
        message: `❌ "${url}" geçersiz URL formatı`
      };
    }
  },
});

// Mock URL Özet Tool (gerçek API olmadan test için)
const mockSummarizeUrl = tool({
  description: 'URL\'yi <PERSON>zetler (test versiyonu)',
  parameters: z.object({
    url: z.string().url().describe('Özetlenecek URL'),
  }),
  execute: async ({ url }) => {
    // Mock özet verisi
    return {
      success: true,
      title: 'Test Sayfa Başlığı',
      summary: `Bu "${url}" adresindeki sayfanın test özetidir. Sayfa içeriği analiz edildi ve önemli noktalar çıkarıldı.`,
      keyPoints: [
        'Ana konu hakkında detaylı bilgi',
        'İstatistiksel veriler ve analizler',
        'Uzman görüşleri ve öneriler'
      ],
      categories: ['Teknoloji', 'İş', 'Analiz'],
      wordCount: 1250,
      readingTime: '5 dakika',
      language: 'tr',
      message: `"${url}" başarıyla özetlendi (test modu)`
    };
  },
});

// BSNS Agent Test Class
class SimpleBsnsAgent {
  private model = openai('gpt-4o-mini');
  
  private systemPrompt = `Sen BSNS URL Özet Agent'ısın. Görevin:

🔗 Kullanıcıdan URL al ve özetle
✅ URL'lerin geçerliliğini kontrol et
📝 Özet sonuçlarını düzenli şekilde sun

Türkçe yanıt ver ve profesyonel ol.`;

  async chat(userMessage: string) {
    try {
      const result = await generateText({
        model: this.model,
        system: this.systemPrompt,
        prompt: userMessage,
        tools: {
          validateUrl,
          mockSummarizeUrl,
        },
        maxToolRoundtrips: 3,
      });

      return {
        text: result.text,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults,
      };
    } catch (error) {
      throw new Error(`BSNS Agent Hatası: ${error.message}`);
    }
  }
}

// Test Fonksiyonu
async function testSimpleBsnsAgent() {
  const agent = new SimpleBsnsAgent();

  console.log('🤖 Basit BSNS Agent Test Başlıyor...\n');

  try {
    // Test 1: Kendini tanıt
    console.log('💬 Test 1: Agent Tanıtımı');
    const intro = await agent.chat('Merhaba! Sen kimsin ve ne yapabilirsin?');
    console.log('🤖 Agent:', intro.text);
    console.log('\n' + '='.repeat(60) + '\n');

    // Test 2: URL Doğrulama
    console.log('🔍 Test 2: URL Doğrulama');
    const validation = await agent.chat('Bu URL geçerli mi: https://www.example.com');
    console.log('🤖 Agent:', validation.text);
    
    if (validation.toolResults && validation.toolResults.length > 0) {
      console.log('\n🔧 Tool Sonuçları:');
      validation.toolResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.toolName}:`, result.result);
      });
    }
    console.log('\n' + '='.repeat(60) + '\n');

    // Test 3: URL Özetleme
    console.log('📄 Test 3: URL Özetleme');
    const summary = await agent.chat('https://www.bbc.com/news sayfasını özetler misin?');
    console.log('🤖 Agent:', summary.text);
    
    if (summary.toolResults && summary.toolResults.length > 0) {
      console.log('\n🔧 Tool Sonuçları:');
      summary.toolResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.toolName}:`, result.result);
      });
    }

    console.log('\n' + '='.repeat(60) + '\n');
    console.log('✅ Basit BSNS Agent Test Tamamlandı!');

  } catch (error) {
    console.error('❌ Test Hatası:', error.message);
  }
}

// Test'i çalıştır
testSimpleBsnsAgent();
