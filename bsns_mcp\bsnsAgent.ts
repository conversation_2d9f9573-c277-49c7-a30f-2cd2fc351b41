// MCP bağlantısını başlatan fonksiyon
export async function initializeMcpConnection() {
  try {
    console.log('🔗 MCP bağlantısı başlatılıyor...');

    const apiUrl = `https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32`;

    const mcpRequest = {
      jsonrpc: "2.0",
      id: 1,
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: "BSNS-MCP-Agent",
          version: "1.0.0"
        }
      }
    };

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json, text/event-stream",
        "User-Agent": "BSNS-MCP-Agent/1.0"
      },
      body: JSON.stringify(mcpRequest),
    });

    if (!response.ok) {
      throw new Error(`API Hatası: ${response.status} - ${response.statusText}`);
    }

    const mcpResponse = await response.json();

    if (mcpResponse.error) {
      throw new Error(`MCP Hatası: ${mcpResponse.error.message || 'Bilinmeyen hata'}`);
    }

    console.log('✅ MCP bağlantısı başarıyla kuruldu');
    return mcpResponse.result;

  } catch (error) {
    console.error('❌ MCP bağlantı hatası:', error.message);
    return { error: error.message };
  }
}

// Zeliha'nın bsns-mcp API'sinden mevcut araçları listeleyen fonksiyon
export async function listAvailableTools() {
  try {
    console.log('🔍 Mevcut araçlar listeleniyor...');

    const apiUrl = `https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32`;

    const mcpRequest = {
      jsonrpc: "2.0",
      id: 2,
      method: "tools/list",
      params: {}
    };

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json, text/event-stream",
        "User-Agent": "BSNS-MCP-Agent/1.0"
      },
      body: JSON.stringify(mcpRequest),
    });

    if (!response.ok) {
      throw new Error(`API Hatası: ${response.status} - ${response.statusText}`);
    }

    const mcpResponse = await response.json();

    if (mcpResponse.error) {
      throw new Error(`MCP Hatası: ${mcpResponse.error.message || 'Bilinmeyen hata'}`);
    }

    console.log('✅ Araçlar başarıyla listelendi');
    return mcpResponse.result;

  } catch (error) {
    console.error('❌ Araç listesi hatası:', error.message);
    return { error: error.message };
  }
}

// Zeliha'nın bsns-mcp API'sine bağlanan basit fonksiyon
export async function summarizeUrlWithZelihaApi(url: string) {
  try {
    console.log('🔗 URL özetleniyor:', url);

    // Zeliha'nın bsns-mcp API URL'si
    const apiUrl = `https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32`;

    // MCP JSON-RPC 2.0 format for tools/call
    const mcpRequest = {
      jsonrpc: "2.0",
      id: 1,
      method: "tools/call",
      params: {
        name: "summarize_url", // Tool name - might need to be adjusted based on actual tool name
        arguments: {
          url: url,
          language: "tr",
          options: {
            maxLength: 500,
            includeKeyPoints: true,
            includeCategories: true
          }
        }
      }
    };

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "BSNS-MCP-Agent/1.0"
      },
      body: JSON.stringify(mcpRequest),
    });

    if (!response.ok) {
      throw new Error(`API Hatası: ${response.status} - ${response.statusText}`);
    }

    const mcpResponse = await response.json();

    // Check for JSON-RPC error
    if (mcpResponse.error) {
      throw new Error(`MCP Hatası: ${mcpResponse.error.message || 'Bilinmeyen hata'}`);
    }

    // Extract result from JSON-RPC response
    const result = mcpResponse.result || {};

    console.log('✅ Özet başarıyla alındı');

    return {
      success: true,
      title: result.title || 'Başlık bulunamadı',
      summary: result.summary || 'Özet oluşturulamadı',
      keyPoints: result.keyPoints || [],
      categories: result.categories || [],
      wordCount: result.wordCount || 0,
      readingTime: result.readingTime || '1 dakika',
      url: url,
      message: `"${result.title || 'Sayfa'}" başarıyla özetlendi.`
    };

  } catch (error) {
    console.error('❌ bsns-mcp API Hatası:', error.message);

    return {
      success: false,
      error: error.message,
      url: url,
      message: 'URL özetlenirken bir hata oluştu. Lütfen URL\'nin geçerli olduğundan emin olun.'
    };
  }
}
