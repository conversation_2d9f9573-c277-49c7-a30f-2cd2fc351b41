import 'dotenv/config';
import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// 1. ROI Calculator Tool
const calculateROI = tool({
  description: 'Calculate Return on Investment (ROI) percentage',
  parameters: z.object({
    initialInvestment: z.number().describe('The initial investment amount'),
    finalValue: z.number().describe('The final value of the investment'),
  }),
  execute: async ({ initialInvestment, finalValue }) => {
    const roi = ((finalValue - initialInvestment) / initialInvestment) * 100;
    const profit = finalValue - initialInvestment;
    
    return {
      roi: parseFloat(roi.toFixed(2)),
      profit: parseFloat(profit.toFixed(2)),
      message: `ROI: ${roi.toFixed(2)}%, Profit: $${profit.toFixed(2)}`
    };
  },
});

// 2. Break-Even Analysis Tool
const calculateBreakEven = tool({
  description: 'Calculate break-even point for a business',
  parameters: z.object({
    fixedCosts: z.number().describe('Total fixed costs per month'),
    pricePerUnit: z.number().describe('Selling price per unit'),
    variableCostPerUnit: z.number().describe('Variable cost per unit'),
  }),
  execute: async ({ fixedCosts, pricePerUnit, variableCostPerUnit }) => {
    const contributionMargin = pricePerUnit - variableCostPerUnit;
    const breakEvenUnits = Math.ceil(fixedCosts / contributionMargin);
    const breakEvenRevenue = breakEvenUnits * pricePerUnit;
    
    return {
      breakEvenUnits,
      breakEvenRevenue: parseFloat(breakEvenRevenue.toFixed(2)),
      contributionMargin: parseFloat(contributionMargin.toFixed(2)),
      message: `Break-even: ${breakEvenUnits} units, $${breakEvenRevenue.toFixed(2)} revenue`
    };
  },
});

// 3. Loan Payment Calculator Tool
const calculateLoanPayment = tool({
  description: 'Calculate monthly loan payment',
  parameters: z.object({
    principal: z.number().describe('Loan amount'),
    annualRate: z.number().describe('Annual interest rate (as percentage, e.g., 5.5)'),
    years: z.number().describe('Loan term in years'),
  }),
  execute: async ({ principal, annualRate, years }) => {
    const monthlyRate = (annualRate / 100) / 12;
    const numberOfPayments = years * 12;
    
    const monthlyPayment = principal * 
      (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
      (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
    
    const totalPayment = monthlyPayment * numberOfPayments;
    const totalInterest = totalPayment - principal;
    
    return {
      monthlyPayment: parseFloat(monthlyPayment.toFixed(2)),
      totalPayment: parseFloat(totalPayment.toFixed(2)),
      totalInterest: parseFloat(totalInterest.toFixed(2)),
      message: `Monthly payment: $${monthlyPayment.toFixed(2)}, Total interest: $${totalInterest.toFixed(2)}`
    };
  },
});

// Advanced Business Assistant with Multiple Tools
class AdvancedBusinessAssistant {
  private model = openai('gpt-4o-mini');
  
  private systemPrompt = `You are an advanced business financial advisor. You can help with:
  - ROI calculations and investment analysis
  - Break-even analysis for business planning
  - Loan payment calculations
  - Financial planning and strategy
  
  Always use the appropriate tools when users ask for calculations. Provide detailed explanations of the results.`;

  async chat(userMessage: string) {
    try {
      const result = await generateText({
        model: this.model,
        system: this.systemPrompt,
        prompt: userMessage,
        tools: {
          calculateROI,
          calculateBreakEven,
          calculateLoanPayment,
        },
        maxToolRoundtrips: 5,
      });

      return {
        text: result.text,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults,
        usage: result.usage,
      };
    } catch (error) {
      throw new Error(`Business Assistant Error: ${error.message}`);
    }
  }
}

// Test All Tools
async function testAllTools() {
  const assistant = new AdvancedBusinessAssistant();

  console.log('🚀 Advanced Business Tools Test\n');

  // Test 1: ROI Calculation
  console.log('📊 Test 1: ROI Calculation');
  const roi = await assistant.chat('I invested $15,000 in equipment and now my business value increased by $20,000. What is my ROI?');
  console.log('Response:', roi.text);
  console.log('Tools used:', roi.toolResults?.length || 0);
  console.log('\n' + '='.repeat(60) + '\n');

  // Test 2: Break-Even Analysis
  console.log('📈 Test 2: Break-Even Analysis');
  const breakeven = await assistant.chat('My fixed costs are $5000/month, I sell products for $50 each, and variable costs are $20 per unit. What is my break-even point?');
  console.log('Response:', breakeven.text);
  console.log('Tools used:', breakeven.toolResults?.length || 0);
  console.log('\n' + '='.repeat(60) + '\n');

  // Test 3: Loan Payment
  console.log('💰 Test 3: Loan Payment Calculation');
  const loan = await assistant.chat('I want to take a $100,000 business loan at 6.5% annual interest for 5 years. What will be my monthly payment?');
  console.log('Response:', loan.text);
  console.log('Tools used:', loan.toolResults?.length || 0);
  console.log('\n' + '='.repeat(60) + '\n');

  console.log('✅ All tools tested successfully!');
}

testAllTools().catch(console.error);
