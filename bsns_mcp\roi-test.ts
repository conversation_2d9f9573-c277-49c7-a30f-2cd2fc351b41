import 'dotenv/config';
import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// Business ROI Calculator Tool
const calculateROI = tool({
  description: 'Calculate Return on Investment (ROI) percentage',
  parameters: z.object({
    initialInvestment: z.number().describe('The initial investment amount'),
    finalValue: z.number().describe('The final value of the investment'),
  }),
  execute: async ({ initialInvestment, finalValue }) => {
    console.log('🔧 Tool executing with:', { initialInvestment, finalValue });
    
    const roi = ((finalValue - initialInvestment) / initialInvestment) * 100;
    const profit = finalValue - initialInvestment;
    
    const result = {
      roi: parseFloat(roi.toFixed(2)),
      profit: parseFloat(profit.toFixed(2)),
      message: `ROI: ${roi.toFixed(2)}%, Profit: $${profit.toFixed(2)}`
    };
    
    console.log('🔧 Tool result:', result);
    return result;
  },
});

async function testROI() {
  console.log('📊 Testing ROI Calculation...\n');

  const result = await generateText({
    model: openai('gpt-4o-mini'),
    system: `You are a helpful business assistant. When users mention investments or financial calculations, use the calculateROI tool and then provide a comprehensive response based on the results.`,
    prompt: 'I invested $10,000 and now my investment is worth $12,500. Can you calculate my ROI and explain what this means?',
    tools: {
      calculateROI,
    },
    maxToolRoundtrips: 3,
  });

  console.log('📝 Response:');
  console.log(result.text);
  
  if (result.toolCalls && result.toolCalls.length > 0) {
    console.log('\n🔧 Tool Calls:');
    result.toolCalls.forEach((call, index) => {
      console.log(`${index + 1}. Tool: ${call.toolName}`);
      console.log(`   Args:`, call.args);
      console.log(`   Result:`, call.result);
    });
  }

  if (result.toolResults && result.toolResults.length > 0) {
    console.log('\n🔧 Tool Results:');
    result.toolResults.forEach((toolResult, index) => {
      console.log(`${index + 1}. Tool Result:`, toolResult);
    });
  }
}

testROI().catch(console.error);
