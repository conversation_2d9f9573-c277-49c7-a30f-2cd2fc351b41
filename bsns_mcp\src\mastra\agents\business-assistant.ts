import { Agent } from '@mastra/core/agent';
import { z } from 'zod';

// Example tool for business calculations
export const calculateROI = {
  name: 'calculate_roi',
  description: 'Calculate Return on Investment (ROI) percentage',
  parameters: z.object({
    initialInvestment: z.number().describe('The initial investment amount'),
    finalValue: z.number().describe('The final value of the investment'),
  }),
  execute: async ({ initialInvestment, finalValue }: { initialInvestment: number; finalValue: number }) => {
    const roi = ((finalValue - initialInvestment) / initialInvestment) * 100;
    return {
      roi: roi.toFixed(2),
      profit: (finalValue - initialInvestment).toFixed(2),
      message: `ROI: ${roi.toFixed(2)}%, Profit: $${(finalValue - initialInvestment).toFixed(2)}`
    };
  },
};

export function createBusinessAssistant(mastra: any) {
  return new Agent({
    name: 'Business Assistant',
    instructions: `You are a helpful business assistant. You can help with:
    - Business planning and strategy
    - Market analysis
    - Financial calculations
    - Project management advice
    - General business questions

    Always provide practical, actionable advice and be professional in your responses.`,
    model: {
      provider: 'openai',
      name: 'gpt-4o-mini',
    },
    tools: [calculateROI],
    mastra,
  });
}
