import 'dotenv/config';
import { summarizeUrlWithZelihaApi } from './bsnsAgent';

/**
 * <PERSON><PERSON><PERSON>'nın bsns-mcp API'si ile URL Özetleme Örneği
 *
 * <PERSON>u dosya, güncellenmiş bsnsAgent'ın nasıl kullanılacağını gösterir.
 * Agent, Zeliha'nın Smithery üzerindeki bsns-mcp API'sine bağlanır.
 */

async function demonstrateAgent() {
  console.log('🤖 BSNS URL Özet Agent - Zeliha API Demo\n');
  console.log('📡 API: https://server.smithery.ai/@zeliha1/bsns-mcp/mcp');
  console.log('🔑 API Key: 6134e80e-8e3e-4eb3-8482-ed8542124c32\n');

  // Özetlenecek URL'ler listesi
  const testUrls = [
    'https://www.example.com',
    'https://www.bbc.com/news',
    'https://www.cnn.com',
    'https://www.hurriyet.com.tr',
    'https://www.milliyet.com.tr'
  ];

  for (let i = 0; i < testUrls.length; i++) {
    const url = testUrls[i];
    console.log(`📄 ${i + 1}. URL Özetleniyor: ${url}`);
    console.log('⏳ Lütfen bekleyin...\n');

    try {
      const result = await summarizeUrlWithZelihaApi(url);

      if (result.success) {
        console.log('✅ Özet Başarılı!');
        console.log(`📰 Başlık: ${result.title}`);
        console.log(`📝 Özet: ${result.summary}`);
        console.log(`📊 Kelime Sayısı: ${result.wordCount}`);
        console.log(`⏱️ Okuma Süresi: ${result.readingTime}`);

        if (result.keyPoints && result.keyPoints.length > 0) {
          console.log('🔑 Anahtar Noktalar:');
          result.keyPoints.forEach((point, index) => {
            console.log(`   ${index + 1}. ${point}`);
          });
        }

        if (result.categories && result.categories.length > 0) {
          console.log('🏷️ Kategoriler:', result.categories.join(', '));
        }
      } else {
        console.log('❌ Özet Başarısız!');
        console.log(`🚫 Hata: ${result.error}`);
        console.log(`💬 Mesaj: ${result.message}`);
      }

    } catch (error) {
      console.log('❌ Beklenmeyen Hata!');
      console.log(`🚫 Hata: ${error.message}`);
    }

    console.log('\n' + '='.repeat(80) + '\n');

    // API'ye çok hızlı istek göndermemek için kısa bir bekleme
    if (i < testUrls.length - 1) {
      console.log('⏳ Sonraki URL için 2 saniye bekleniyor...\n');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  console.log('🎉 Demo tamamlandı!');
  console.log('\n📋 Kullanım Örnekleri:');
  console.log('```typescript');
  console.log('import { summarizeUrlWithZelihaApi } from "./bsnsAgent";');
  console.log('');
  console.log('// Basit kullanım');
  console.log('const result = await summarizeUrlWithZelihaApi("https://example.com");');
  console.log('console.log(result);');
  console.log('```');
}

// Demo'yu çalıştır
demonstrateAgent().catch(console.error);
