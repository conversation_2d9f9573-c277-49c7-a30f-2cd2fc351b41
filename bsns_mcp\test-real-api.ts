import 'dotenv/config';
import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import axios from 'axios';

// Gerçek URL Özet Tool - bsns-mcp API'si ile
const summarizeUrl = tool({
  description: 'Verilen URL\'yi bsns-mcp API\'sine gönderir ve özet alır',
  parameters: z.object({
    url: z.string().url().describe('Özetlenecek web sayfasının URL\'si'),
    language: z.string().optional().default('tr').describe('Özet dili (tr, en, vb.)'),
  }),
  execute: async ({ url, language = 'tr' }) => {
    try {
      console.log('🔗 URL özetleniyor:', url);
      console.log('🌐 API\'ye istek gönderiliyor...');
      
      // bsns-mcp API'sine istek gönder
      const response = await axios.post('http://localhost:3001/api/summarize', {
        url: url,
        language: language,
        options: {
          maxLength: 500,
          includeKeyPoints: true,
          includeCategories: true
        }
      }, {
        timeout: 30000, // 30 saniye timeout
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'BSNS-MCP-Agent/1.0'
        }
      });

      const summary = response.data;
      
      console.log('✅ Özet başarıyla alındı!');
      
      return {
        success: true,
        title: summary.title || 'Başlık bulunamadı',
        summary: summary.summary || 'Özet oluşturulamadı',
        keyPoints: summary.keyPoints || [],
        categories: summary.categories || [],
        wordCount: summary.wordCount || 0,
        readingTime: summary.readingTime || '1 dakika',
        url: url,
        language: language,
        message: `"${summary.title}" başlıklı sayfa başarıyla özetlendi.`
      };
      
    } catch (error) {
      console.error('❌ API Hatası:', error.message);
      
      // Detaylı hata analizi
      if (error.code === 'ECONNREFUSED') {
        return {
          success: false,
          error: 'bsns-mcp API\'sine bağlanılamadı. API\'nin http://localhost:3001 adresinde çalıştığından emin olun.',
          url: url,
          message: '🚨 API Bağlantı Hatası: Lütfen bsns-mcp API\'sini başlatın.'
        };
      }
      
      if (error.response?.status === 404) {
        return {
          success: false,
          error: 'API endpoint bulunamadı.',
          url: url,
          message: '🚨 API Endpoint Hatası: /api/summarize endpoint\'i bulunamadı.'
        };
      }
      
      if (error.response?.status === 400) {
        return {
          success: false,
          error: 'Geçersiz istek formatı.',
          url: url,
          message: '🚨 İstek Hatası: URL formatı veya parametreler hatalı.'
        };
      }
      
      return {
        success: false,
        error: error.message || 'Bilinmeyen hata',
        url: url,
        message: '🚨 Genel Hata: URL özetlenirken beklenmeyen bir hata oluştu.'
      };
    }
  },
});

// API Durumu Kontrol Tool
const checkApiStatus = tool({
  description: 'bsns-mcp API\'sinin çalışıp çalışmadığını kontrol eder',
  parameters: z.object({}),
  execute: async () => {
    try {
      const response = await axios.get('http://localhost:3001/health', {
        timeout: 5000
      });
      
      return {
        status: 'online',
        message: '✅ bsns-mcp API çalışıyor',
        apiVersion: response.data?.version || 'unknown',
        uptime: response.data?.uptime || 'unknown'
      };
    } catch (error) {
      return {
        status: 'offline',
        message: '❌ bsns-mcp API çalışmıyor',
        error: error.message,
        suggestion: 'API\'yi http://localhost:3001 adresinde başlatın'
      };
    }
  },
});

// Gerçek API Test Class
class RealApiTest {
  private model = openai('gpt-4o-mini');
  
  private systemPrompt = `Sen BSNS URL Özet Agent'ısın. Gerçek bsns-mcp API'si ile çalışıyorsun.

GÖREVLER:
1. 🔍 API durumunu kontrol et
2. 🔗 URL'leri özetle
3. 📝 Sonuçları kullanıcıya sun

Her zaman Türkçe yanıt ver.`;

  async chat(userMessage: string) {
    try {
      const result = await generateText({
        model: this.model,
        system: this.systemPrompt,
        prompt: userMessage,
        tools: {
          summarizeUrl,
          checkApiStatus,
        },
        maxToolRoundtrips: 3,
      });

      return {
        text: result.text,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults,
      };
    } catch (error) {
      throw new Error(`API Test Hatası: ${error.message}`);
    }
  }
}

// Test Fonksiyonu
async function testRealApi() {
  const agent = new RealApiTest();

  console.log('🚀 Gerçek bsns-mcp API Test Başlıyor...\n');

  try {
    // Test 1: API Durumu
    console.log('🔍 Test 1: API Durumu Kontrolü');
    const apiStatus = await agent.chat('bsns-mcp API\'si çalışıyor mu kontrol et');
    console.log('🤖 Agent:', apiStatus.text);
    
    if (apiStatus.toolResults && apiStatus.toolResults.length > 0) {
      console.log('\n🔧 API Durum Sonucu:');
      apiStatus.toolResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.toolName}:`, result.result);
      });
    }
    console.log('\n' + '='.repeat(60) + '\n');

    // Test 2: URL Özetleme
    console.log('📄 Test 2: URL Özetleme (Gerçek API)');
    const summary = await agent.chat('https://www.example.com sayfasını özetler misin?');
    console.log('🤖 Agent:', summary.text);
    
    if (summary.toolResults && summary.toolResults.length > 0) {
      console.log('\n🔧 Özet Sonucu:');
      summary.toolResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.toolName}:`, result.result);
      });
    }

    console.log('\n' + '='.repeat(60) + '\n');
    console.log('✅ Gerçek API Test Tamamlandı!');

  } catch (error) {
    console.error('❌ Test Hatası:', error.message);
  }
}

// Test'i çalıştır
testRealApi();
