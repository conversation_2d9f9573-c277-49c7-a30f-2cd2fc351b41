{"name": "bsns_mcp", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "<PERSON>ra dev", "build": "mastra build", "example": "tsx example.ts", "simple-test": "tsx simple-test.ts", "business": "tsx business-assistant.ts", "roi": "tsx roi-test.ts", "advanced": "tsx advanced-business-tools.ts", "test-bsns": "tsx test-bsns-agent.ts", "simple-bsns": "tsx simple-bsns-test.ts", "test-real-api": "tsx test-real-api.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@mastra/core": "^0.10.0", "@mastra/libsql": "^0.10.0", "@mastra/memory": "^0.10.0", "ai": "^4.3.16", "axios": "^1.9.0", "dotenv": "^16.5.0", "zod": "^3.25.30"}, "devDependencies": {"@types/node": "^22.15.23", "mastra": "^0.10.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}}