import 'dotenv/config';

async function debugZelihaApi() {
  console.log('🔍 Zeliha\'nın bsns-mcp API Debug Başlıyor...\n');
  
  const apiUrl = `https://server.smithery.ai/@zeliha1/bsns-mcp/mcp?api_key=6134e80e-8e3e-4eb3-8482-ed8542124c32`;
  
  console.log('🔗 API URL:', apiUrl);
  console.log('\n' + '='.repeat(80) + '\n');

  try {
    // Test 1: GET request to see what the server responds with
    console.log('📡 Test 1: GET Request');
    const getResponse = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "User-Agent": "BSNS-MCP-Agent/1.0"
      }
    });
    
    console.log('Status:', getResponse.status, getResponse.statusText);
    console.log('Headers:', Object.fromEntries(getResponse.headers.entries()));
    
    const getBody = await getResponse.text();
    console.log('Body:', getBody.substring(0, 500) + (getBody.length > 500 ? '...' : ''));
    console.log('\n' + '='.repeat(80) + '\n');

    // Test 2: POST with empty body
    console.log('📡 Test 2: POST with empty body');
    const emptyPostResponse = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "BSNS-MCP-Agent/1.0"
      },
      body: JSON.stringify({})
    });
    
    console.log('Status:', emptyPostResponse.status, emptyPostResponse.statusText);
    console.log('Headers:', Object.fromEntries(emptyPostResponse.headers.entries()));
    
    const emptyPostBody = await emptyPostResponse.text();
    console.log('Body:', emptyPostBody.substring(0, 500) + (emptyPostBody.length > 500 ? '...' : ''));
    console.log('\n' + '='.repeat(80) + '\n');

    // Test 3: POST with simple URL parameter
    console.log('📡 Test 3: POST with simple URL parameter');
    const simplePostResponse = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "BSNS-MCP-Agent/1.0"
      },
      body: JSON.stringify({
        url: "https://www.example.com"
      })
    });
    
    console.log('Status:', simplePostResponse.status, simplePostResponse.statusText);
    console.log('Headers:', Object.fromEntries(simplePostResponse.headers.entries()));
    
    const simplePostBody = await simplePostResponse.text();
    console.log('Body:', simplePostBody.substring(0, 500) + (simplePostBody.length > 500 ? '...' : ''));
    console.log('\n' + '='.repeat(80) + '\n');

    // Test 4: Different Content-Type
    console.log('📡 Test 4: POST with different Content-Type');
    const altContentTypeResponse = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "User-Agent": "BSNS-MCP-Agent/1.0"
      },
      body: "url=https://www.example.com"
    });
    
    console.log('Status:', altContentTypeResponse.status, altContentTypeResponse.statusText);
    console.log('Headers:', Object.fromEntries(altContentTypeResponse.headers.entries()));
    
    const altContentTypeBody = await altContentTypeResponse.text();
    console.log('Body:', altContentTypeBody.substring(0, 500) + (altContentTypeBody.length > 500 ? '...' : ''));

  } catch (error) {
    console.error('❌ Debug Hatası:', error);
  }
}

// Debug'u çalıştır
debugZelihaApi();
