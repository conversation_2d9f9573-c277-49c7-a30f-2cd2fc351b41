import 'dotenv/config';
import { bsnsAgent } from './src/mastra/index';

async function testBsnsAgent() {
  console.log('🤖 BSNS URL Özet Agent Test Başlıyor...\n');

  try {
    // Test 1: URL Doğrulama
    console.log('📋 Test 1: URL Doğrulama');
    const validationTest = await bsnsAgent.generate(
      'Bu URL geçerli mi kontrol eder misin: https://www.example.com'
    );
    console.log('🔍 Doğrulama Sonucu:');
    console.log(validationTest.text);
    
    if (validationTest.toolResults && validationTest.toolResults.length > 0) {
      console.log('\n🔧 Tool Sonuçları:');
      validationTest.toolResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.toolName}:`, result.result);
      });
    }
    
    console.log('\n' + '='.repeat(80) + '\n');

    // Test 2: URL Özetleme (Mock test - API olmadan)
    console.log('📄 Test 2: URL Özetleme Talebi');
    const summaryTest = await bsnsAgent.generate(
      'https://www.bbc.com/news sayfasını özetler misin?'
    );
    console.log('📝 Özet Sonucu:');
    console.log(summaryTest.text);
    
    if (summaryTest.toolResults && summaryTest.toolResults.length > 0) {
      console.log('\n🔧 Tool Sonuçları:');
      summaryTest.toolResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.toolName}:`, result.result);
      });
    }

    console.log('\n' + '='.repeat(80) + '\n');

    // Test 3: Genel Sohbet
    console.log('💬 Test 3: Genel Sohbet');
    const chatTest = await bsnsAgent.generate(
      'Merhaba! Sen kimsin ve ne yapabilirsin?'
    );
    console.log('🤖 Agent Yanıtı:');
    console.log(chatTest.text);

    console.log('\n' + '='.repeat(80) + '\n');
    console.log('✅ BSNS Agent Test Tamamlandı!');

  } catch (error) {
    console.error('❌ Test Hatası:', error);
    console.error('Hata Detayı:', error.message);
  }
}

// Test'i çalıştır
testBsnsAgent();
